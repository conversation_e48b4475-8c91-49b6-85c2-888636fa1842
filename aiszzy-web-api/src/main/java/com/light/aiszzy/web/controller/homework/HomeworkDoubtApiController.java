package com.light.aiszzy.web.controller.homework;

import com.light.aiszzy.homeworkResult.api.HomeworkDoubtApi;
import com.light.aiszzy.homeworkResult.api.HomeworkPageResultApi;
import com.light.aiszzy.homeworkResult.api.HomeworkResultAnswerApi;
import com.light.aiszzy.homeworkResult.api.HomeworkResultApi;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkPageResultBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkPageResultConditionBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultAnswerBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultAnswerConditionBo;
import com.light.contants.AISzzyConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.security.service.CurrentUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@Validated
@Api(value = "", tags = "疑问项")
public class HomeworkDoubtApiController {

    @Resource
    private CurrentUserService currentUserService;

    @Resource
    private HomeworkResultApi homeworkResultApi;

    @Resource
    private HomeworkPageResultApi homeworkPageResultApi;

    @Resource
    private HomeworkResultAnswerApi homeworkResultAnswerApi;

    @Resource
    private HomeworkDoubtApi homeworkDoubtApi;

    @PostMapping("/doubt/noStudent")
    @ApiOperation(value = "学生未匹配", httpMethod = "POST")
    public AjaxResult noStudent(@RequestBody HomeworkPageResultConditionBo bo) {
        bo.setOrgCode(currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getCode());
        bo.setDoubtType(AISzzyConstants.DoubtType.NOERROR.getType());
        return (bo.getPageNo() != null && bo.getPageNo() == -1) ? homeworkPageResultApi.getHomeworkPageResultListByCondition(bo) : homeworkPageResultApi.getHomeworkPageResultPageListByCondition(bo);
    }

    @PostMapping("/doubt/repeat")
    @ApiOperation(value = "学生重复", httpMethod = "POST")
    public AjaxResult repeat(@RequestBody HomeworkPageResultConditionBo bo) {
        bo.setOrgCode(currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getCode());
        bo.setIsDelete(StatusEnum.NOTDELETE.getCode());
        bo.setIsDoubt(AISzzyConstants.DoubtStatus.EXIST.getType());
        bo.setDoubtType(AISzzyConstants.DoubtType.REPEAT.getType());
        // 学生重复
        return (bo.getPageNo() != null && bo.getPageNo() == -1) ? homeworkPageResultApi.getHomeworkPageResultListByCondition(bo) : homeworkPageResultApi.getHomeworkPageResultPageListByCondition(bo);
    }

    @PostMapping("/doubt/answer")
    @ApiOperation(value = "题目疑问", httpMethod = "POST")
    public AjaxResult answer(@RequestBody HomeworkResultAnswerConditionBo bo) {
        bo.setIsDoubt(AISzzyConstants.DoubtStatus.EXIST.getType());
        return  (bo.getPageNo() != null && bo.getPageNo() == -1) ? homeworkResultAnswerApi.getHomeworkResultAnswerListByCondition(bo) : homeworkResultAnswerApi.getHomeworkResultAnswerPageListByCondition(bo);
    }

    @PostMapping("/doubt/dealNoStudent")
    @ApiOperation(value = "处理学生未匹配", httpMethod = "POST")
    public AjaxResult dealNoStudent(@RequestBody HomeworkPageResultBo bo) {
        return homeworkPageResultApi.dealNoStudent(bo);
    }

    @PostMapping("/doubt/dealRepeat")
    @ApiOperation(value = "处理学生重复", httpMethod = "POST")
    public AjaxResult dealRepeat(@RequestBody HomeworkPageResultBo bo) {
        return homeworkPageResultApi.dealRepeat(bo);
    }

    @PostMapping("/doubt/dealAnswer")
    @ApiOperation(value = "处理题目疑问", httpMethod = "POST")
    public AjaxResult dealAnswer(@RequestBody HomeworkResultAnswerBo bo) {
        return homeworkResultAnswerApi.updateHomeworkResultAnswer(bo);
    }

    /**
     * 统计疑问项
     * @param homeworkOid 作业Oid
     * @return 统计结果
     */
    @GetMapping("/doubt/stat")
    @ApiOperation(value = "统计疑问项", httpMethod = "GET")
    public AjaxResult countDoubt(@RequestParam("homeworkOid") String homeworkOid){
        return homeworkDoubtApi.stat(homeworkOid);
    }
}
