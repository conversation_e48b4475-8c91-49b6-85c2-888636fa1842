package com.light.aiszzy.web.controller.basicInfo;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import com.alibaba.fastjson.JSONObject;
import com.light.aiszzy.web.model.*;
import com.light.aiszzy.web.utils.DateKit;
import com.light.aiszzy.web.utils.EasyPoiUtil;
import com.light.aiszzy.web.utils.ExcelKit;
import com.light.aiszzy.web.utils.NumberKit;
import com.light.base.area.api.AreaApi;
import com.light.base.config.service.ConfigApiService;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.utils.FuzzyQueryUtil;
import com.light.core.utils.PwdUtil;
import com.light.enums.Enable;
import com.light.enums.TeacherImportTemplateType;
import com.light.enums.UserIdentityType;
import com.light.security.service.CurrentUserService;
import com.light.user.clazz.api.ClazzHeadmasterApi;
import com.light.user.clazz.entity.vo.ClazzHeadmasterVo;
import com.light.user.edu.api.EducationApi;
import com.light.user.edu.entity.bo.EducationBo;
import com.light.user.edu.entity.vo.EducationVo;
import com.light.user.teacher.api.TeacherApi;
import com.light.user.teacher.api.TeacherClassesSubjectApi;
import com.light.user.teacher.entity.bo.TeacherBo;
import com.light.user.teacher.entity.vo.TeacherClassesSubjectVo;
import com.light.user.teacher.entity.vo.TeacherVo;
import com.light.user.user.api.UserApi;
import com.light.user.user.entity.bo.UserBo;
import com.light.user.user.entity.vo.UserVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;


@Api(tags = "老师")
@RestController
@RequestMapping("teacher")
@Slf4j
public class TeacherApiController {

    /**
     * 导入错误信息提示
     */
    String ERROR_IMPORT_MESSAGE = "请根据模板内容正确填写信息，可能的错误类型如下：\n" +
            "1）表头内容被修改或被删除；\n" +
            "2）新增sheet表格；\n" +
            "3）未删除示例数据。";

    String DATE_FORMAT_STYLE_1 = "yyyy-MM-dd";
    String DATE_FORMAT_STYLE_2 = "yyyy-MM-dd HH:mm:ss";
    String DATE_FORMAT_STYLE_3 = "yyyy年MM月dd日";
    String DATE_FORMAT_STYPE_4="yyyy年MM月";

    @Resource
    private TeacherApi teacherApi;

    @Autowired
    private ConfigApiService configApiService;

    @Resource
    private EducationApi educationApi;
    @Resource
    private AreaApi areaApi;

    @Resource
    private CurrentUserService currentUserService;

    @Resource
    private TeacherClassesSubjectApi teacherClassesSubjectApi;

    @Resource
    private ClazzHeadmasterApi clazzHeadmasterApi;

    @Resource
    private UserApi userApi;


    @ApiOperation("当前老师信息")
    @GetMapping("current")
    public AjaxResult current() {
        String currentOid = this.currentUserService.getCurrentOid();
        AjaxResult<UserVo> userVoAjaxResult = this.userApi.getVoByUserOid(currentOid);
        if(userVoAjaxResult.isFail() || userVoAjaxResult.getData() == null) {
            return userVoAjaxResult;
        }
        UserVo userVo = userVoAjaxResult.getData();
        Map<String, Object> map = new HashMap<>();
        map.put("user", userVo);
        // 任教信息
        Optional.ofNullable(this.teacherClassesSubjectApi.getByUserOid(currentOid).getData())
                .ifPresent(x->   map.put("teacherClassesSubjectList", x));

        // 班主任信息
        Optional.ofNullable(this.clazzHeadmasterApi.getByUserOid(currentOid).getData())
                .ifPresent(x->   map.put("classHeadmasterList", x));
        return AjaxResult.success(map);
    }

    /**
     * 查看教师默认密码
     *
     * @return org detail
     * @throws Exception the exception
     * <AUTHOR>
     * @date 2022 -03-29 10:21:26
     */
    @ApiOperation(value = "查看教师默认密码", httpMethod = "GET")
    @ResponseBody
    @RequestMapping(value = "/default-pwd", method = RequestMethod.GET)
    public AjaxResult getDefaultPwd() throws Exception {
        String param = configApiService.getConfigValue(SystemConstants.USER_PASSWORD_PARAMS).getData();
        final String passwordParamInitialPwd = PwdUtil.getFixedPwd(param);
        return AjaxResult.success(passwordParamInitialPwd);
    }

    /**
     * 下载教师的导入模板
     *
     * @param type 模板类型{@link TeacherImportTemplateType}
     * @param request the request
     * @param response the response
     * @throws IOException the io exception
     * <AUTHOR>
     * @date 2022 -03-30 15:50:18
     */
    @GetMapping("/template")
    @ApiOperation(value = "下载教师导入模板", httpMethod = "GET")
    public void teacherTemplate(
            @ApiParam(name = "type", value = "模板类型:1校教学人员,2校非教学人员,3教育局",
                    required = true) @RequestParam("type") Integer type,
            HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {
            String teacherTemplateName = TeacherImportTemplateType.getTemplateName(type) + ".xls";
            URI uri = new URI(null, null, teacherTemplateName, null);
            OutputStream out = response.getOutputStream();
            response.reset();
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.addHeader("Access-Control-Allow-Origin", request.getHeader("Origin"));
            response.addHeader("Access-Control-Allow-Headers", "*");
            response.addHeader("Access-Control-Allow-Methods", "*");
            response.addHeader("Access-Control-Allow-Credentials", "true");
            response.setHeader("Content-Disposition",
                    "attachment; filename=" + uri.toASCIIString() + ";filename*=utf-8''" + uri.toASCIIString());
            response.setContentType("application/vnd.ms-excel");
            String templateFileName = TeacherImportTemplateType.getTemplateFileName(type);
            InputStream in = ResourceUtil.getStream("template/" + templateFileName);
            // 后续如果需要修改模板可以在这里改写流
            IoUtil.copy(in, out);
        } catch (SecurityException | IllegalArgumentException | URISyntaxException e) {
            log.error("/teacher/template error1:", e);
        } catch (Exception e) {
            log.error("/teacher/template error2:", e);
        }
    }

    /**
     * 导入教师
     *
     * @param file the file
     * @param organizationId 组织机构id
     * @param type 1校教学人员,2校非教学人员,3教育局
     * @return ajax result
     * @throws IOException the io exception
     * <AUTHOR>
     * @date 2022 -04-15 13:20:53
     */
    @PostMapping("/import")
    @ResponseBody
    @ApiOperation(value = "导入教师", httpMethod = SystemConstants.POST_REQUEST)
    public AjaxResult importTeacherUser(HttpServletRequest request, MultipartFile file, Long organizationId,
                                        Integer type) throws Exception {
        if(organizationId == null){
            organizationId =  currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getOrganizationId();
        }

        ImportParams params = new ImportParams();
        // 表头设置为2行
        params.setHeadRows(2);
        params.setTitleRows(1);
        params.setNeedVerify(true);

        List<TeacherBo> teacherBos = new ArrayList<>();
        TeacherImportJXModel model = new TeacherImportJXModel();
        int errorCount = 0;

        if (type.equals(TeacherImportTemplateType.SCHOOL_JX.getValue())) {
            ExcelImportResult<TeacherImportJXModel> result;
            try {
                result = ExcelImportUtil.importExcelMore(file.getInputStream(), TeacherImportJXModel.class, params);
            } catch (Exception e) {
                return AjaxResult.fail(ERROR_IMPORT_MESSAGE);
            }
            List<TeacherImportJXModel> list = result.getList();
            for (TeacherImportJXModel jx : list) {
                try {
                    TeacherBo teacherBo = transferTeacherImportModel2TeacherBo(jx, organizationId);
                    teacherBo.setType(Long.valueOf("1"));
                    teacherBos.add(teacherBo);
                } catch (Exception e) {
                    errorCount++;
                }
            }
            if (CollectionUtils.isNotEmpty(result.getFailList())) {
                errorCount += result.getFailList().stream()
                        .filter(obj -> StringUtils.isNotBlank(obj.getName()) || StringUtils.isNotBlank(obj.getPhone()))
                        .count();
            }
        } else if (type.equals(TeacherImportTemplateType.SCHOOL_FJX.getValue())) {
            ExcelImportResult<TeacherImportFJXModel> result;
            try {
                result = ExcelImportUtil.importExcelMore(file.getInputStream(), TeacherImportFJXModel.class, params);
            } catch (Exception e) {
                return AjaxResult.fail(ERROR_IMPORT_MESSAGE);
            }
            List<TeacherImportFJXModel> list = result.getList();
            for (TeacherImportFJXModel fjx : list) {
                try {
                    BeanUtils.copyProperties(fjx, model);
                    TeacherBo teacherBo = transferTeacherImportModel2TeacherBo(model, organizationId);
                    teacherBo.setType(Long.valueOf("2"));
                    teacherBos.add(teacherBo);
                } catch (Exception e) {
                    errorCount++;
                }
            }
            if (CollectionUtils.isNotEmpty(result.getFailList())) {
                errorCount += result.getFailList().stream()
                        .filter(obj -> StringUtils.isNotBlank(obj.getName()) || StringUtils.isNotBlank(obj.getPhone()))
                        .count();
            }
        } else if (type.equals(TeacherImportTemplateType.BUREAU.getValue())) {
            ExcelImportResult<TeacherImportBureauModel> result;
            try {
                result = ExcelImportUtil.importExcelMore(file.getInputStream(), TeacherImportBureauModel.class, params);
            } catch (Exception e) {
                return AjaxResult.fail(ERROR_IMPORT_MESSAGE);
            }
            List<TeacherImportBureauModel> list = result.getList();
            for (TeacherImportBureauModel bureau : list) {
                try {
                    BeanUtils.copyProperties(bureau, model);
                    TeacherBo teacherBo = transferTeacherImportModel2TeacherBo(model, organizationId);
                    teacherBo.setType(Long.valueOf("1"));
                    teacherBos.add(teacherBo);
                } catch (Exception e) {
                    errorCount++;
                }
            }
            if (CollectionUtils.isNotEmpty(result.getFailList())) {
                errorCount += result.getFailList().stream()
                        .filter(obj -> StringUtils.isNotBlank(obj.getName()) || StringUtils.isNotBlank(obj.getPhone()))
                        .count();
            }
        }
        if (CollectionUtils.isEmpty(teacherBos)) {
            AjaxResult ajaxResult = new AjaxResult();
            ajaxResult.setMsg("成功导入0条数据，另有" + errorCount + "条数据由于姓名和手机号字段填写有误，导入失败");
            return ajaxResult;
        }
        AjaxResult ajaxResult = teacherApi.addBatchTeacher(teacherBos);
        if (ajaxResult.isFail()) {
            ajaxResult.setMsg(ajaxResult.getMsg() + ERROR_IMPORT_MESSAGE);
            return ajaxResult;
        }
        AjaxResult result = new AjaxResult();
        String sb = "成功导入" + teacherBos.size() + "条数据，另有" + errorCount + "条数据由于姓名或手机号字段填写有误，导入失败";
        result.setMsg(sb);
        return result;
    }

    /**
     * 对象转换
     *
     * @param teacherImportJXModel the teacher import model
     * @param organizationId the organization id
     * @return teacher bo
     * <AUTHOR>
     * @date 2022 -04-15 13:56:04
     */
    private TeacherBo transferTeacherImportModel2TeacherBo(TeacherImportJXModel teacherImportJXModel,
                                                           Long organizationId) {
        TeacherBo teacherBo = new TeacherBo();
        List<EducationBo> educationList = new ArrayList<>();
        EducationBo educationBo = new EducationBo();
        UserBo userBo = new UserBo();
        userBo.setRealName(teacherImportJXModel.getName());
        userBo.setPhone(teacherImportJXModel.getPhone());
        userBo.setSex(NumberKit.str2Integer(teacherImportJXModel.getSex()));
        userBo.setBirthday(DateKit.string2Date(teacherImportJXModel.getBirthday(), DATE_FORMAT_STYLE_3));
        userBo.setNation(NumberKit.str2Integer(teacherImportJXModel.getNation()));
        userBo.setNationalityId(NumberKit.str2Long(teacherImportJXModel.getNationalityId()));
        userBo.setNativePlaceId(teacherImportJXModel.getNativePlaceId());
        userBo.setIdentityType(teacherImportJXModel.getIdentityType());
        userBo.setIdentityCardNumber(teacherImportJXModel.getIdentityCardNumber());
        userBo.setRegisteredResidence(teacherImportJXModel.getRegisteredResidence());
        userBo.setRegistrationType(NumberKit.str2Integer(teacherImportJXModel.getRegistrationType()));
        userBo.setOverseasChinese(NumberKit.str2Integer(teacherImportJXModel.getOverseasChinese()));
        userBo.setPoliticalOutlook(NumberKit.str2Integer(teacherImportJXModel.getPoliticalOutlookType()));
        userBo.setMaritalStatus(NumberKit.str2Integer(teacherImportJXModel.getMaritalType()));
        userBo.setReligiousBeliefId(teacherImportJXModel.getReligiousBelief());
        userBo.setHealthStatus(NumberKit.str2Integer(teacherImportJXModel.getHealthType()));
        userBo.setHomeAddress(teacherImportJXModel.getHomeAddress());
        userBo.setEmail(teacherImportJXModel.getPostalCode());
        userBo.setOrganizationId(organizationId);
        userBo.setUserIdentityType(UserIdentityType.TEACHER.getValue());
        userBo.setPosition(teacherImportJXModel.getPosition());
        String param = configApiService.getConfigValue(SystemConstants.USER_PASSWORD_PARAMS).getData();
        final String passwordParamInitialPwd = PwdUtil.getFixedPwd(param);
        userBo.setPassword(passwordParamInitialPwd);
        teacherBo.setGeneratorAccount(true);
        teacherBo.setUser(userBo);
        // 学历
        educationBo.setEducationBackground(NumberKit.str2Integer(teacherImportJXModel.getEducation()));
        educationBo.setGraduateSchool(teacherImportJXModel.getGraduateSchool());
        educationBo.setEducationSystem(teacherImportJXModel.getEducationSystem());
        educationBo.setMajor(teacherImportJXModel.getMajor());
        educationBo
                .setJoinDate(DateKit.string2Date(teacherImportJXModel.getJoinDate(), DATE_FORMAT_STYPE_4));
        educationBo.setGraduationDate(
                DateKit.string2Date(teacherImportJXModel.getGraduationDate(), DATE_FORMAT_STYPE_4));
        educationList.add(educationBo);
        teacherBo.setEducationList(educationList);
        // 教学教师模板类包含所有字段，
        teacherBo.setQualificationType(NumberKit.str2Integer(teacherImportJXModel.getQualificationType()));
        teacherBo.setQualificationSubject(teacherImportJXModel.getQualificationSubject());
        teacherBo
                .setWorkDate(DateKit.string2Date(teacherImportJXModel.getWorkDate(), DATE_FORMAT_STYPE_4));
        teacherBo.setEducationDate(
                DateKit.string2Date(teacherImportJXModel.getEducationDate(), DATE_FORMAT_STYPE_4));
        teacherBo.setSchoolDate(
                DateKit.string2Date(teacherImportJXModel.getSchoolDate(), DATE_FORMAT_STYPE_4));
        teacherBo.setEmployeeType(NumberKit.str2Integer(teacherImportJXModel.getEmployeeType()));
        teacherBo.setIsQuotas(NumberKit.str2Integer(teacherImportJXModel.getIsQuotas()));
        teacherBo.setQuotasType(NumberKit.str2Integer(teacherImportJXModel.getQuotasType()));
        teacherBo.setTitleType(NumberKit.str2Integer(teacherImportJXModel.getTitleType()));
        teacherBo.setSkill(NumberKit.str2Integer(teacherImportJXModel.getSkill()));
        teacherBo.setSubject(teacherImportJXModel.getNowSubject());
        teacherBo.setPastSubject(teacherImportJXModel.getAgoSubject());
        return teacherBo;
    }

    /**
     * 导出教职工
     *
     * @param teacherConditionBoExt, response
     * @return void
     * <AUTHOR>
     * @date 2022/4/21 17:55
     */
    @PostMapping("/export")
    @ApiOperation(value = "教师导出", httpMethod = "POST")
    public void exportUser(@RequestBody TeacherConditionBoExt teacherConditionBoExt, HttpServletResponse response)
            throws Exception {
        if (StringUtils.isNotBlank(teacherConditionBoExt.getRealName())) {
            teacherConditionBoExt.setRealName(FuzzyQueryUtil.transferMean(teacherConditionBoExt.getRealName()));
        }
        teacherConditionBoExt.setIsDelete(Enable.NO.getValue());
        teacherConditionBoExt.setOrderBy("CONVERT(real_name USING GBK)");
        teacherConditionBoExt.setPageNo(SystemConstants.NO_PAGE);
        AjaxResult ajaxResult = teacherApi.getTeacherListByCondition(teacherConditionBoExt);
        int type = teacherConditionBoExt.getType().intValue();
        if (ajaxResult.isFail()) {
            return;
        }
        Map<String, Object> result = (Map<String, Object>)ajaxResult.getData();
        List<Map> list = (List)result.get("list");
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        List<TeacherVo> teacherVos = JSONObject.parseArray(JSONObject.toJSONString(list), TeacherVo.class);

        // 判断当前单位是教育局还是学校教师或者非教师
        String fileName = "";
        if (TeacherImportTemplateType.SCHOOL_JX.getValue() == type) {
            fileName = "教学人员";
        } else if (TeacherImportTemplateType.SCHOOL_FJX.getValue() == type) {
            fileName = "校非教学人员";
        } else if (TeacherImportTemplateType.BUREAU.getValue() == type) {
            fileName = "教育局";
        }
        List<TeacherExportVo> teacherExportVos = new ArrayList<>();
        for (TeacherVo teacherVo : teacherVos) {
            TeacherExportVo jXModel = transferTeacherVo2StudentExportVo(teacherVo);
            teacherExportVos.add(jXModel);
        }

        // 判断字段是否有值，有值则前端勾选，不隐藏
        for (TeacherExportVo teacherExportVo : teacherExportVos) {
            TeacherImportJXModel teacherImportJXModel = teacherConditionBoExt.getTeacherImportJXModel();
            EasyPoiUtil<TeacherExportVo> easyPoiUtil = new EasyPoiUtil<>();
            easyPoiUtil.t = teacherExportVo;
            Map<String, String> describe = org.apache.commons.beanutils.BeanUtils.describe(teacherImportJXModel);
            Set<String> keySet = describe.keySet();
            for (String s : keySet) {
                // 排除class转换Map时默认新增的class key
                if (!s.equals("class")) {
                    if (null == describe.get(s)) {
                        easyPoiUtil.hihdColumn(s, true);
                    } else {
                        easyPoiUtil.hihdColumn(s, false);
                    }
                }
            }
        }
        ExcelKit.exportExcel2Response(teacherExportVos, null, fileName, TeacherExportVo.class, fileName, response);

    }

    /**
     * 教师信息转换为对象
     *
     * @param teacherVo
     * @return java.lang.Object
     * <AUTHOR>
     * @date 2022/4/21 18:13
     */
    public TeacherExportVo transferTeacherVo2StudentExportVo(TeacherVo teacherVo) {
        TeacherExportVo teacherImportJXModel = new TeacherExportVo();
        UserVo userVo = teacherVo.getUserVo();
        teacherImportJXModel.setName(userVo.getRealName());
        teacherImportJXModel.setPhone(userVo.getPhone());
        teacherImportJXModel.setSex(NumberKit.integer2Str(userVo.getSex()));
        teacherImportJXModel.setBirthday(DateKit.date2String(userVo.getBirthday(), DATE_FORMAT_STYLE_3));
        teacherImportJXModel.setNation(NumberKit.integer2Str(userVo.getNation()));
        teacherImportJXModel.setNationalityId(NumberKit.long2Str(userVo.getNationalityId()));
        teacherImportJXModel.setNativePlaceId(userVo.getNativePlaceId());
        teacherImportJXModel.setIdentityType(userVo.getIdentityType());
        teacherImportJXModel.setIdentityCardNumber(userVo.getIdentityCardNumber());
        String registeredResidence = "";
        if (userVo.getRegisteredResidenceProvince() != null || userVo.getRegisteredResidenceCity() != null
                || userVo.getRegisteredResidenceArea() != null) {
            String prov = areaApi.getAreaNameFromCache(userVo.getRegisteredResidenceProvince()).getData();
            String city = areaApi.getAreaNameFromCache(userVo.getRegisteredResidenceCity()).getData();
            String area = areaApi.getAreaNameFromCache(userVo.getRegisteredResidenceArea()).getData();
            registeredResidence = registeredResidence.concat(prov == null ? "" : prov).concat(",")
                    .concat(city == null ? "" : city).concat(",").concat(area == null ? "" : area).concat(" ");
        }
        registeredResidence = userVo.getRegisteredResidence() == null ? registeredResidence
                : registeredResidence.concat(userVo.getRegisteredResidence());
        teacherImportJXModel.setRegisteredResidence(registeredResidence);
        teacherImportJXModel.setRegistrationType(NumberKit.integer2Str(userVo.getRegistrationType()));
        teacherImportJXModel.setOverseasChinese(NumberKit.integer2Str(userVo.getOverseasChinese()));
        teacherImportJXModel.setPoliticalOutlookType(NumberKit.integer2Str(userVo.getPoliticalOutlook()));
        teacherImportJXModel.setMaritalType(NumberKit.integer2Str(userVo.getMaritalStatus()));
        teacherImportJXModel.setReligiousBelief(userVo.getReligiousBeliefId());
        teacherImportJXModel.setHealthType(NumberKit.integer2Str(userVo.getHealthStatus()));
        String homeAddress = "";
        if (userVo.getHomeAddressProvince() != null) {
            String prov = areaApi.getAreaNameFromCache(userVo.getHomeAddressProvince()).getData();
            String city = areaApi.getAreaNameFromCache(userVo.getHomeAddressCity()).getData();
            String area = areaApi.getAreaNameFromCache(userVo.getHomeAddressArea()).getData();
            homeAddress = homeAddress.concat(prov == null ? "" : prov).concat(",").concat(city == null ? "" : city)
                    .concat(",").concat(area == null ? "" : area).concat(" ");
        }
        homeAddress = userVo.getHomeAddress() == null ? homeAddress : homeAddress.concat(userVo.getHomeAddress());
        teacherImportJXModel.setHomeAddress(homeAddress);
        teacherImportJXModel.setPostalCode(userVo.getEmail());
        teacherImportJXModel.setPosition(userVo.getPosition());

        // 学历
        List<String> arrayList = new ArrayList<>();
        arrayList.add(teacherVo.getUserOid());
        AjaxResult ajaxResult = educationApi.getByUserOidList(arrayList);
        if (ajaxResult.isFail()) {
            return teacherImportJXModel;
        }
        List<EducationVo> educationVos =
                JSONObject.parseArray(JSONObject.toJSONString(ajaxResult.getData()), EducationVo.class);
        if (CollectionUtils.isNotEmpty(educationVos)) {
            EducationVo educationVo = educationVos.get(0);
            teacherImportJXModel.setEducation(NumberKit.integer2Str(educationVo.getEducationBackground()));
            teacherImportJXModel.setGraduateSchool(educationVo.getGraduateSchool());
            teacherImportJXModel.setEducationSystem(educationVo.getEducationSystem());
            teacherImportJXModel.setMajor(educationVo.getMajor());
            teacherImportJXModel
                    .setJoinDate(DateKit.date2String(educationVo.getJoinDate(), DATE_FORMAT_STYPE_4));
            teacherImportJXModel.setGraduationDate(
                    DateKit.date2String(educationVo.getGraduationDate(), DATE_FORMAT_STYPE_4));
        }

        // 教学教师模板类包含所有字段，
        teacherImportJXModel.setQualificationType(NumberKit.integer2Str(teacherVo.getQualificationType()));
        teacherImportJXModel.setQualificationSubject(teacherVo.getQualificationSubject());
        teacherImportJXModel
                .setWorkDate(DateKit.date2String(teacherVo.getWorkDate(), DATE_FORMAT_STYPE_4));
        teacherImportJXModel
                .setEducationDate(DateKit.date2String(teacherVo.getEducationDate(), DATE_FORMAT_STYPE_4));
        teacherImportJXModel
                .setSchoolDate(DateKit.date2String(teacherVo.getSchoolDate(), DATE_FORMAT_STYPE_4));
        teacherImportJXModel.setEmployeeType(NumberKit.integer2Str(teacherVo.getEmployeeType()));
        teacherImportJXModel.setIsQuotas(NumberKit.integer2Str(teacherVo.getIsQuotas()));
        teacherImportJXModel.setQuotasType(NumberKit.integer2Str(teacherVo.getQuotasType()));
        teacherImportJXModel.setTitleType(NumberKit.integer2Str(teacherVo.getTitleType()));
        teacherImportJXModel.setSkill(NumberKit.integer2Str(teacherVo.getSkill()));
        teacherImportJXModel.setAgoSubject(teacherVo.getPastSubject());
        return teacherImportJXModel;
    }
}
