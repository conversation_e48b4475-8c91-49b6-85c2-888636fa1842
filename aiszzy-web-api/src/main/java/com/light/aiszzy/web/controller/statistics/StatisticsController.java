package com.light.aiszzy.web.controller.statistics;


import com.github.pagehelper.PageInfo;
import com.light.aiszzy.resourcesUserAddToCart.entity.bo.ResourcesUserAddToCartConditionBo;
import com.light.aiszzy.resourcesUserAddToCart.entity.vo.ResourcesUserAddToCartVo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@Validated
@Api(value = "", tags = "统计相关" )
public class StatisticsController {

    @PostMapping("/home/<USER>")
    @ApiOperation(value = "首页题目统计",httpMethod = "POST")
    public AjaxResult homeStatistics(){
        Map<String,Object> map = new HashMap<String,Object>();
        map.put("zzyt",0);
        map.put("zzsj",0);
        map.put("zzbg",0);
        map.put("zzjp",0);
        return AjaxResult.success(map);
    }
}
