package com.light.aiszzy.web.controller.homework;

import javax.validation.constraints.NotNull;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import com.light.core.constants.SystemConstants;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;

import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import java.util.List;
import com.github.pagehelper.PageInfo;

import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultAnswerConditionBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultAnswerBo;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultAnswerVo;
import com.light.aiszzy.homeworkResult.service.HomeworkResultAnswerApiService;


/**
 * 学生题目答案表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-08 09:40:42
 */
@RestController
@Validated
@Api(value = "", tags = "学生题目答案表接口" )
public class HomeworkResultAnswerApiController {
	
    @Autowired
    private HomeworkResultAnswerApiService homeworkResultAnswerApiService;

	@PostMapping("/homeworkResultAnswer/pageList")
	@ApiOperation(value = "分页查询学生题目答案表",httpMethod = "POST")
	public AjaxResult<PageInfo<HomeworkResultAnswerVo>> getHomeworkResultAnswerPageListByCondition(@RequestBody HomeworkResultAnswerConditionBo condition){
		return homeworkResultAnswerApiService.getHomeworkResultAnswerPageListByCondition(condition);
    }

	@PostMapping("/homeworkResultAnswer/list")
	@ApiOperation(value = "查询所有学生题目答案表",httpMethod = "POST")
	public AjaxResult<List<HomeworkResultAnswerVo>> getHomeworkResultAnswerAllListByCondition(@RequestBody HomeworkResultAnswerConditionBo condition){
		return homeworkResultAnswerApiService.getHomeworkResultAnswerListByCondition(condition);
	}

	@PostMapping("/homeworkResultAnswer/add")
	@ApiOperation(value = "新增学生题目答案表",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "新增学生题目答案表", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult addHomeworkResultAnswer(@Validated @RequestBody HomeworkResultAnswerBo homeworkResultAnswerBo){
		return homeworkResultAnswerApiService.addHomeworkResultAnswer(homeworkResultAnswerBo);
    }

	@PostMapping("/homeworkResultAnswer/update")
	@ApiOperation(value = "修改学生题目答案表",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "修改学生题目答案表", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult updateHomeworkResultAnswer(@Validated @RequestBody HomeworkResultAnswerBo homeworkResultAnswerBo) {
		return homeworkResultAnswerApiService.updateHomeworkResultAnswer(homeworkResultAnswerBo);
	}

	@GetMapping("/homeworkResultAnswer/detail")
	@ApiOperation(value = "查询学生题目答案表详情",httpMethod = "GET")
	@ApiImplicitParam(name = "homeworkResultAnswerId", value = "oid", required = true, dataType = "String", paramType = "query")
	public AjaxResult<HomeworkResultAnswerVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid) {
		return homeworkResultAnswerApiService.getDetail(oid);
	}

	@GetMapping("/homeworkResultAnswer/delete")
	@ApiOperation(value = "删除学生题目答案表",httpMethod = "GET")
	@ApiImplicitParam(name = "oid", value = "oid", required = true, dataType = "String", paramType = "delete")
	@OperationLogAnnotation(moduleName = "删除学生题目答案表", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid) {
		return homeworkResultAnswerApiService.delete(oid);
	}
}
