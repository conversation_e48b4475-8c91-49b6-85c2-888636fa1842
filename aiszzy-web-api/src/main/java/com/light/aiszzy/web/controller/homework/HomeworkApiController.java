package com.light.aiszzy.web.controller.homework;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageInfo;
import com.light.aiszzy.homework.api.HomeworkClassApi;
import com.light.aiszzy.homework.entity.bo.*;
import com.light.aiszzy.homework.entity.vo.HomeworkQuestionVo;
import com.light.aiszzy.homework.entity.vo.HomeworkVo;
import com.light.aiszzy.homework.service.HomeworkApiService;
import com.light.aiszzy.homework.service.HomeworkQuestionApiService;
import com.light.aiszzy.resourcesUserAddToCart.entity.bo.ResourcesUserAddToCartBo;
import com.light.aiszzy.web.config.AiszzyThreadExecutor;
import com.light.base.attachment.entity.vo.AttachmentVo;
import com.light.base.attachment.service.AttachmentApiService;
import com.light.core.entity.AjaxResult;
import com.light.core.utils.StringUtils;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import com.light.security.service.CurrentUserService;
import com.light.user.clazz.api.ClazzHeadmasterApi;
import com.light.user.teacher.api.TeacherClassesSubjectApi;
import com.light.utils.HtmlToPdfUtil;
import com.light.utils.PdfUtil;
import com.light.utils.VelocityUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.zip.ZipOutputStream;


/**
 * 作业表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:31:30
 */
@RestController
@Validated
@Api(value = "", tags = "作业表接口")
public class HomeworkApiController {

    @Autowired
    private HomeworkApiService homeworkApiService;

    @Autowired
    private HomeworkQuestionApiService homeworkQuestionApiService;

    @Resource
    private CurrentUserService currentUserService;

    @Resource
    private AttachmentApiService attachmentApiService;

    @Resource
    private TeacherClassesSubjectApi teacherClassesSubjectApi;

    @Resource
    private ClazzHeadmasterApi clazzHeadmasterApi;

    @Resource
    private HomeworkClassApi homeworkClassApi;


    @Resource(name = AiszzyThreadExecutor.EXECUTOR_GENERATE_ANALYSIS)
    private Executor generateAnalysisExecutor;

    @PostMapping("/homework/pageList")
    @ApiOperation(value = "分页查询作业表", httpMethod = "POST")
    public AjaxResult<PageInfo<HomeworkVo>> getHomeworkPageListByCondition(@RequestBody HomeworkConditionBo condition) {
        if (StringUtils.isEmpty(condition.getOrderBy())) {
            condition.setOrderBy("create_time desc");
        }
        condition.setOrgCode(currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getCode());
        return homeworkApiService.getHomeworkPageListByCondition(condition);
    }

    @PostMapping("/homework/list")
    @ApiOperation(value = "查询所有作业表", httpMethod = "POST")
    public AjaxResult<List<HomeworkVo>> getHomeworkAllListByCondition(@RequestBody HomeworkConditionBo condition) {
        if (StringUtils.isEmpty(condition.getOrderBy())) {
            condition.setOrderBy("create_time desc");
        }
        condition.setOrgCode(currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getCode());
        return homeworkApiService.getHomeworkListByCondition(condition);
    }

    @PostMapping("/homework/listForTeacher")
    @ApiOperation(value = "根据作业本查看老师对应作业", httpMethod = "POST")
    public AjaxResult listForTeacher(@RequestBody HomeworkConditionBo condition) {
        String currentOid = this.currentUserService.getCurrentOid();
        condition.setUserOid(currentOid);

        Set<Long> classIds = new HashSet<>();
        // 任教信息
        Optional.ofNullable(this.teacherClassesSubjectApi.getByUserOid(currentOid).getData())
                .ifPresent(x -> x.forEach(y -> classIds.add(y.getClassesId())));

        // 班主任信息
        Optional.ofNullable(this.clazzHeadmasterApi.getByUserOid(currentOid).getData())
                .ifPresent(x -> x.forEach(y -> classIds.add(y.getClassesId())));
        if (CollectionUtil.isEmpty(classIds)) {
            return AjaxResult.success(Collections.emptyList());
        }
        condition.setClassIds(StrUtil.join(",", classIds));
        return homeworkApiService.getHomeworkResultPageListByCondition(condition);
    }

    @PostMapping("/homework/listResult")
    @ApiOperation(value = "查询作业扫描结果", httpMethod = "POST")
    public AjaxResult<PageInfo<HomeworkVo>> getHomeworkResultPageListByCondition(@RequestBody HomeworkConditionBo condition) {
        if (StringUtils.isEmpty(condition.getOrderBy())) {
            condition.setOrderBy("create_time desc");
        }
        return homeworkApiService.getHomeworkResultPageListByCondition(condition);
    }

    @PostMapping("/homework/listForBind")
    @ApiOperation(value = "查所有绑定和未绑定作业", httpMethod = "POST")
    public AjaxResult listForBind(@RequestBody HomeworkConditionBo condition) {
        if (StringUtils.isEmpty(condition.getOrgCode())) {
            condition.setOrgCode(currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getCode());
        }
        return homeworkApiService.listForBind(condition);
    }

    @PostMapping("/homework/listBindAndNoBind")
    @ApiOperation(value = "查所有绑定和未绑定作业", httpMethod = "POST")
    public AjaxResult listBindAndNoBind(@RequestBody HomeworkConditionBo condition) {
        if (StringUtils.isEmpty(condition.getOrderBy())) {
            condition.setOrderBy("create_time desc");
        }
        if (StringUtils.isEmpty(condition.getOrgCode())) {
            condition.setOrgCode(currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getCode());
        }
        return homeworkApiService.listBindAndNoBind(condition);
    }

    @PostMapping("/homework/add")
    @ApiOperation(value = "新增作业表", httpMethod = "POST")
    @OperationLogAnnotation(moduleName = "新增作业表", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult addHomework(@Validated @RequestBody HomeworkBo homeworkBo) {
        if (StringUtils.isEmpty(homeworkBo.getOrgCode())) {
            homeworkBo.setOrgCode(currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getCode());
        }
        homeworkBo.setCreateByRealName(currentUserService.getCurrentUser().getCurrentUser().getRealName());

        return homeworkApiService.addHomework(homeworkBo);
    }

    @PostMapping("/homework/batchUpdateStatus")
    @ApiOperation(value = "批量修改作业状态", httpMethod = "POST")
    @OperationLogAnnotation(moduleName = "批量修改作业状态", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult batchUpdateStatus(@Validated @RequestBody HomeworkBo homeworkBo) {
        if (StringUtils.isEmpty(homeworkBo.getOrgCode())) {
            homeworkBo.setOrgCode(currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getCode());
        }
        return homeworkApiService.batchUpdateStatus(homeworkBo);
    }

    @PostMapping("/homework/update")
    @ApiOperation(value = "修改作业表", httpMethod = "POST")
    @OperationLogAnnotation(moduleName = "修改作业表", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult updateHomework(@Validated @RequestBody HomeworkBo homeworkBo) {
        if (StringUtils.isEmpty(homeworkBo.getOrgCode())) {
            homeworkBo.setOrgCode(currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getCode());
        }
        return homeworkApiService.updateHomework(homeworkBo);
    }

    @PostMapping("/homework/finish")
    @ApiOperation(value = "完成制卡", httpMethod = "POST")
    @OperationLogAnnotation(moduleName = "完成制卡", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult finishHomework(@Validated @RequestBody HomeworkBo homeworkBo) {
        //调用端生成pdf，防止生成时间过长，内部接口调用失败
        AjaxResult<HomeworkVo> detail = homeworkApiService.getDetail(homeworkBo.getOid());
        if (detail.isFail()) {
            return detail;
        }
        HomeworkVo homeworkVo = detail.getData();

        if (StringUtils.isNotEmpty(homeworkVo.getHomeworkPdfUrl())) {
            return AjaxResult.fail("已经制卡完成,操作错误");
        }

        if (StringUtils.isEmpty(homeworkBo.getPaperJson())) {
            return AjaxResult.fail("参数错误");
        }
        JSONArray jsonArr = JSONUtil.parseArray(homeworkBo.getPaperJson());

        List<byte[]> bytesArr = new ArrayList<>();
        for (int i = 0; i < jsonArr.size(); i++) {
            JSONObject jsonObject = jsonArr.getJSONObject(i);
            String htmlData = jsonObject.getStr("pdfHtml");
            byte[] bytes = homeworkVo.getPageSize().equalsIgnoreCase("A4") ? HtmlToPdfUtil.exportPdfA4(htmlData) : (homeworkVo.getPageSize().equalsIgnoreCase("A3") ? HtmlToPdfUtil.exportPdfA3(htmlData) : null);
            if (bytes != null) {
                bytesArr.add(bytes);
            }
        }
        ByteArrayOutputStream homeworkOutputStream = PdfUtil.mergeBytePDFs(bytesArr);
        if (homeworkOutputStream != null) {
            MultipartFile multipartFile = new MockMultipartFile("file", homeworkVo.getHomeworkName() + ".pdf", MediaType.MULTIPART_FORM_DATA_VALUE, homeworkOutputStream.toByteArray());
            AjaxResult<AttachmentVo> attachmentResult = this.attachmentApiService.uploadFileFromPath(multipartFile, "/file/homework");
            if (attachmentResult.isSuccess()) {
                AttachmentVo data = attachmentResult.getData();
                homeworkBo.setHomeworkPdfUrl(data.getNginxUrl());
            }
        }
        CompletableFuture.runAsync(() -> {
            HomeworkQuestionConditionBo bo = new HomeworkQuestionConditionBo();
            bo.setHomeworkOid(homeworkVo.getOid());
            AjaxResult<List<HomeworkQuestionVo>> homeworkQuestions = homeworkQuestionApiService.getHomeworkQuestionListByCondition(bo);
            if (homeworkQuestions.isSuccess() && CollectionUtil.isNotEmpty(homeworkQuestions.getData())) {
                List<HomeworkQuestionVo> questionList = homeworkQuestions.getData();
                questionList.sort(Comparator.comparing(HomeworkQuestionVo::getQuesOrderNum));
                Map<String, Object> map = new HashMap<>();
                List<Map> list = new ArrayList<>();
                map.put("homeworkName", homeworkVo.getHomeworkName());

                for (HomeworkQuestionVo questionVo : questionList) {
                    Map<String, Object> m = new HashMap<>();
                    m.put("queNo", questionVo.getQuesOrderNum());
                    m.put("answer", questionVo.getQuesAnswer());
                    m.put("answerType", questionVo.getQuesAnswerType());
                    m.put("analysis", questionVo.getAnalysisAnswer());
                    m.put("analysisType", questionVo.getAnalysisAnswerType());
                    list.add(m);
                }
                map.put("questions", list);
                String velocityContext = VelocityUtil.getVelocityContext(map, "template/PaperAnalysis.vm");
                MultipartFile answerMultipartFile = new MockMultipartFile("file", homeworkVo.getHomeworkName() + "-答案解析.docx", MediaType.MULTIPART_FORM_DATA_VALUE, velocityContext.getBytes(StandardCharsets.UTF_8));
                AjaxResult<AttachmentVo> answerAttachmentResult = this.attachmentApiService.uploadFileFromPath(answerMultipartFile, "/file/answer");
                if (answerAttachmentResult.isSuccess()) {
                    AttachmentVo answerData = answerAttachmentResult.getData();
                    HomeworkBo hbo = new HomeworkBo();
                    hbo.setOid(homeworkVo.getOid());
                    hbo.setHomeworkAnswerPdfUrl(answerData.getNginxUrl());
                    homeworkApiService.updateHomework(hbo);
                }
            }

        }, generateAnalysisExecutor);

        if (StringUtils.isEmpty(homeworkBo.getHomeworkPdfUrl())) {
            return AjaxResult.fail("制卡生成pdf失败");
        }
        homeworkBo.setPaperJson(null);
        return homeworkApiService.updateHomework(homeworkBo);
    }

    @GetMapping("/homework/detail")
    @ApiOperation(value = "查询作业表详情", httpMethod = "GET")
    @ApiImplicitParam(name = "homeworkId", value = "oid", required = true, dataType = "String", paramType = "query")
    public AjaxResult<HomeworkVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid) {
        return homeworkApiService.getDetail(oid);
    }

    @GetMapping("/homework/delete")
    @ApiOperation(value = "删除作业表", httpMethod = "GET")
    @ApiImplicitParam(name = "oid", value = "oid", required = true, dataType = "String", paramType = "delete")
    @OperationLogAnnotation(moduleName = "删除作业表", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid) {
        return homeworkApiService.delete(oid);
    }

    @GetMapping("/homework/changeQuestion")
    @ApiOperation(value = "作业换题", httpMethod = "GET")
    @ApiImplicitParam(name = "oid", value = "oid", required = true, dataType = "String", paramType = "delete")
    @OperationLogAnnotation(moduleName = "作业换题", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult changeQuestion(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid, @RequestParam("oldQuestionOid") String oldQuestionOid, @RequestParam("newQuestionOid") String newQuestionOid) {
        return homeworkQuestionApiService.changeQuestion(oid, oldQuestionOid, newQuestionOid);
    }

    @GetMapping("/homework/deleteQuestion")
    @ApiOperation(value = "删除作业题目", httpMethod = "GET")
    @ApiImplicitParam(name = "oid", value = "oid", required = true, dataType = "String", paramType = "delete")
    @OperationLogAnnotation(moduleName = "删除作业题目", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult deleteQuestion(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid, @RequestParam("questionOid") String questionOid) {
        return homeworkQuestionApiService.deleteQuestion(oid, questionOid);
    }

    @PostMapping("/homework/cartHomework")
    @ApiOperation(value = "试题篮作业", httpMethod = "POST")
    @ApiImplicitParam(name = "cartOid", value = "cartOid", required = true, dataType = "String", paramType = "delete")
    @OperationLogAnnotation(moduleName = "试题篮作业", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult<HomeworkVo> cartHomework(@RequestBody ResourcesUserAddToCartBo bo) {
        bo.setUserOid(currentUserService.getCurrentOid());
        bo.setOrgCode(currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getCode());
        bo.setCreateByRealName(currentUserService.getCurrentUser().getCurrentUser().getRealName());
        return homeworkApiService.cartHomework(bo);
    }

    @GetMapping("/homework/copyHomework")
    @ApiOperation(value = "复制作业", httpMethod = "GET")
    @ApiImplicitParam(name = "homeworkOid", value = "homeworkOid", required = true, dataType = "String", paramType = "delete")
    @OperationLogAnnotation(moduleName = "复制作业", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult<HomeworkVo> copyHomework(@RequestParam("oid") String oid) {
        HomeworkBo homeworkBo = new HomeworkBo();
        homeworkBo.setUserOid(currentUserService.getCurrentOid());
        homeworkBo.setOrgCode(currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getCode());
        homeworkBo.setCreateByRealName(currentUserService.getCurrentUser().getCurrentUser().getRealName());

        return homeworkApiService.copyHomework(homeworkBo);
    }

    @PostMapping("/homework/originalHomework")
    @ApiOperation(value = "生成原题作业", httpMethod = "POST")
    @OperationLogAnnotation(moduleName = "生成原题作业", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult originalHomework(@Validated @RequestBody HomeworkBo homeworkBo) {
        return homeworkApiService.originalHomework(homeworkBo);
    }

    @PostMapping("/homework/layeredHomework")
    @ApiOperation(value = "生成分层作业", httpMethod = "POST")
    @OperationLogAnnotation(moduleName = "生成分层作业", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult layeredHomework(@Validated @RequestBody HomeworkBo homeworkBo) {
        return homeworkApiService.layeredHomework(homeworkBo);
    }

    @PostMapping("/homework/schoolHomework")
    @ApiOperation(value = "生成校本作业", httpMethod = "POST")
    @OperationLogAnnotation(moduleName = "生成校本作业", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult schoolHomework(@Validated @RequestBody HomeworkBo homeworkBo) {
        return homeworkApiService.schoolHomework(homeworkBo);
    }

    @PostMapping("/homework/targetHomework")
    @ApiOperation(value = "生成靶向作业", httpMethod = "POST")
    @OperationLogAnnotation(moduleName = "生成靶向作业", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult targetHomework(@Validated @RequestBody HomeworkBo homeworkBo) {
        return null;
    }

    @GetMapping("/homework/classInfo")
    @ApiOperation(value = "班级作业信息", httpMethod = "GET")
    @OperationLogAnnotation(moduleName = "生成靶向作业", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult classInfo(@RequestParam("HomeworkOid") String HomeworkOid) {
        HomeworkClassConditionBo bo = new HomeworkClassConditionBo();
        bo.setHomeworkOid(HomeworkOid);
        return homeworkClassApi.getHomeworkClassListByCondition(bo);
    }

    @GetMapping("/homework/classInfoTeacher")
    @ApiOperation(value = "老师班级作业信息", httpMethod = "GET")
    @OperationLogAnnotation(moduleName = "生成靶向作业", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult classInfoTeacher(@RequestParam("HomeworkOid") String HomeworkOid) {

        String currentOid = this.currentUserService.getCurrentOid();
        List<Long> classIds = new ArrayList<>();
        // 任教信息
        Optional.ofNullable(this.teacherClassesSubjectApi.getByUserOid(currentOid).getData())
                .ifPresent(x -> x.forEach(y -> classIds.add(y.getClassesId())));

        // 班主任信息
        Optional.ofNullable(this.clazzHeadmasterApi.getByUserOid(currentOid).getData())
                .ifPresent(x -> x.forEach(y -> classIds.add(y.getClassesId())));

        if (CollectionUtil.isEmpty(classIds)) {
            return AjaxResult.success(Collections.emptyList());
        }
        HomeworkClassConditionBo bo = new HomeworkClassConditionBo();
        bo.setClassIds(StrUtil.join(",", classIds));
        bo.setHomeworkOid(HomeworkOid);
        return homeworkClassApi.getHomeworkClassListByCondition(bo);
    }


    @GetMapping("/homework/downloadZip")
    @ApiOperation(value = "下载作业或答案zip", httpMethod = "GET")
    public void downloadZip(String oid, Boolean homeworkPdf, Boolean answerPdf, HttpServletResponse response) {
        try {
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setContentType("application/zip; charset=utf-8");

            AjaxResult<HomeworkVo> detail = homeworkApiService.getDetail(oid);

            if (detail.getSuccess()) {
                HomeworkVo homeworkVo = detail.getData();
                String fileName = homeworkVo.getHomeworkName() + ".zip";
                List<String> pathArr = new ArrayList<>();
                List<InputStream> insArr = new ArrayList<>();
                if (homeworkPdf && StringUtils.isNotEmpty(homeworkVo.getHomeworkPdfUrl())) {
                    pathArr.add(homeworkVo.getHomeworkName().trim() + ".pdf");
                    insArr.add(new ByteArrayInputStream(HttpUtil.downloadBytes(homeworkVo.getHomeworkPdfUrl())));
                }
                if (answerPdf && StringUtils.isNotEmpty(homeworkVo.getHomeworkAnswerPdfUrl())) {
                    pathArr.add(homeworkVo.getHomeworkName().trim() + "—答案解析.docx");
                    insArr.add(new ByteArrayInputStream(HttpUtil.downloadBytes(homeworkVo.getHomeworkAnswerPdfUrl())));
                }

                response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + URLUtil.encode(fileName));
                ZipOutputStream zipOutputStream = ZipUtil.getZipOutputStream(response.getOutputStream(), CharsetUtil.CHARSET_UTF_8);
                ZipUtil.zip(zipOutputStream, pathArr.toArray(new String[0]), insArr.toArray(new InputStream[0]));
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
