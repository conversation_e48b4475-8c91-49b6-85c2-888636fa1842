package com.light.aiszzy.web.controller.homework;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.poi.excel.StyleSet;
import com.light.aiszzy.homework.entity.vo.HomeworkVo;
import com.light.aiszzy.homework.service.HomeworkApiService;
import com.light.aiszzy.homeworkResult.api.HomeworkResultAnswerApi;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultAnswerConditionBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultBo;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultAnswerVo;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultVo;
import com.light.aiszzy.homeworkResult.service.HomeworkResultApiService;
import com.light.aiszzy.statistics.entity.bo.HomeworkClassQuestionStatisticsBo;
import com.light.aiszzy.statistics.entity.vo.HomeworkClassQuestionStatisticsVo;
import com.light.aiszzy.statistics.service.HomeworkClassQuestionStatisticsApiService;
import com.light.base.dictionary.api.DictionaryDataApi;
import com.light.core.entity.AjaxResult;
import com.light.enums.CorrectResultEnum;
import com.light.redis.component.DictTranslationComponent;
import com.light.redis.entity.Dict;
import com.light.user.clazz.api.ClazzApi;
import com.light.user.clazz.entity.vo.ClazzVo;
import com.light.user.student.api.StudentApi;
import com.light.user.student.entity.vo.StudentVo;
import com.light.user.user.entity.vo.UserVo;
import io.swagger.annotations.Api;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import com.github.pagehelper.PageInfo;

import com.light.aiszzy.homework.entity.bo.HomeworkQuestionConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkQuestionBo;
import com.light.aiszzy.homework.entity.vo.HomeworkQuestionVo;
import com.light.aiszzy.homework.service.HomeworkQuestionApiService;


/**
 * 作业题目信息
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-22 19:28:21
 */
@RestController
@Validated
@Api(value = "", tags = "作业题目信息接口")
public class HomeworkQuestionApiController {

    @Autowired
    private HomeworkQuestionApiService homeworkQuestionApiService;

    @Autowired
    private HomeworkApiService homeworkApiService;

    @Autowired
    private HomeworkResultApiService homeworkResultService;

    @Autowired
    private HomeworkClassQuestionStatisticsApiService homeworkClassQuestionStatisticsApiService;
    @Autowired
    private HomeworkResultAnswerApi HomeworkResultAnswerApi;

    @Autowired
    private StudentApi studentApi;

    @Autowired
    private ClazzApi clazzApi;

    @Autowired
    private DictTranslationComponent dictTranslationComponent;

    @PostMapping("/homeworkQuestion/pageList")
    @ApiOperation(value = "分页查询作业题目信息", httpMethod = "POST")
    public AjaxResult<PageInfo<HomeworkQuestionVo>> getHomeworkQuestionPageListByCondition(@RequestBody HomeworkQuestionConditionBo condition) {
        return homeworkQuestionApiService.getHomeworkQuestionPageListByCondition(condition);
    }

    @PostMapping("/homeworkQuestion/list")
    @ApiOperation(value = "查询所有作业题目信息", httpMethod = "POST")
    public AjaxResult<List<HomeworkQuestionVo>> getHomeworkQuestionAllListByCondition(@RequestBody HomeworkQuestionConditionBo condition) {
        return homeworkQuestionApiService.getHomeworkQuestionListByCondition(condition);
    }

    @PostMapping("/homeworkQuestion/commentList")
    @ApiOperation(value = "讲评查询所有作业题目信息", httpMethod = "POST")
    public AjaxResult<List<HomeworkQuestionVo>> commentList(@RequestBody HomeworkQuestionConditionBo condition) {
        return homeworkQuestionApiService.commentList(condition);
    }

    @PostMapping("/homeworkQuestion/add")
    @ApiOperation(value = "新增作业题目信息", httpMethod = "POST")
    @OperationLogAnnotation(moduleName = "新增作业题目信息", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult addHomeworkQuestion(@Validated @RequestBody HomeworkQuestionBo homeworkQuestionBo) {
        return homeworkQuestionApiService.addHomeworkQuestion(homeworkQuestionBo);
    }

    @PostMapping("/homeworkQuestion/update")
    @ApiOperation(value = "修改作业题目信息", httpMethod = "POST")
    @OperationLogAnnotation(moduleName = "修改作业题目信息", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult updateHomeworkQuestion(@Validated @RequestBody HomeworkQuestionBo homeworkQuestionBo) {
        return homeworkQuestionApiService.updateHomeworkQuestion(homeworkQuestionBo);
    }

    @GetMapping("/homeworkQuestion/detail")
    @ApiOperation(value = "查询作业题目信息详情", httpMethod = "GET")
    @ApiImplicitParam(name = "oid", value = "oid", required = true, dataType = "String", paramType = "query")
    public AjaxResult<HomeworkQuestionVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid) {
        return homeworkQuestionApiService.getDetail(oid);
    }

    @GetMapping("/homeworkQuestion/detailSimilar")
    @ApiOperation(value = "查询作业题目信息详情补相似题", httpMethod = "GET")
    @ApiImplicitParam(name = "oid", value = "oid", required = true, dataType = "String", paramType = "query")
    public AjaxResult<HomeworkQuestionVo> detailSimilar(@NotNull(message = "请选择数据") @RequestParam("oid") String oid) {
        return homeworkQuestionApiService.detailSimilar(oid);
    }

    @GetMapping("/homeworkQuestion/delete")
    @ApiOperation(value = "删除作业题目信息", httpMethod = "GET")
    @ApiImplicitParam(name = "oid", value = "oid", required = true, dataType = "String", paramType = "delete")
    @OperationLogAnnotation(moduleName = "删除作业题目信息", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid) {
        return homeworkQuestionApiService.delete(oid);
    }

    @ApiOperation("下载讲评报告")
    @ResponseBody
    @RequestMapping(value = "/homeworkQuestion/downLoadComment", method = {RequestMethod.POST})
    public void downLoadComment(@RequestBody HomeworkQuestionConditionBo condition, HttpServletResponse response) {
        try {
            ExcelWriter writer = ExcelUtil.getWriter(true);
            //查询班级学生
            AjaxResult<List<StudentVo>> studentAllListByClassesId = studentApi.getStudentAllListByClassesId(condition.getClassId());
            if (studentAllListByClassesId.isFail()) {
                return;
            }
            List<StudentVo> studentAllList = studentAllListByClassesId.getData();
            //查询班级
            AjaxResult clazzAjaxResult = clazzApi.getDetail(condition.getClassId());
            if (clazzAjaxResult.isFail()) {
                return;
            }
            ClazzVo clazzVo = JSONUtil.toBean(JSONUtil.toJsonStr(clazzAjaxResult.getData()), ClazzVo.class);
            //查询作业
            AjaxResult<HomeworkVo> detail = homeworkApiService.getDetail(condition.getHomeworkOid());
            if (detail.isFail()) {
                return;
            }
            HomeworkResultAnswerConditionBo homeworkResultAnswerConditionBo = new HomeworkResultAnswerConditionBo();
            homeworkResultAnswerConditionBo.setHomeworkOid(condition.getHomeworkOid());
            homeworkResultAnswerConditionBo.setStuClassId(condition.getClassId());
            //查询用户答案
            AjaxResult<List<HomeworkResultAnswerVo>> homeworkResultAnswerListByCondition = HomeworkResultAnswerApi.getHomeworkResultAnswerListByCondition(homeworkResultAnswerConditionBo);
            if (homeworkResultAnswerListByCondition.isFail()) {
                return;
            }
            List<HomeworkResultAnswerVo> homeworkResultAnswerList = homeworkResultAnswerListByCondition.getData();
            Map<String, HomeworkResultAnswerVo> answerMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(homeworkResultAnswerList)) {
                answerMap = homeworkResultAnswerList.stream().collect(Collectors.toMap(p1 -> p1.getStuOid() + "_" + p1.getQuestionOid(), p2 -> p2, (p1, p2) -> p1));
            }
            HomeworkVo homeworkVo = detail.getData();
            Dict bookGrade = dictTranslationComponent.getDict("book_grade", homeworkVo.getGrade().toString());
            String name = homeworkVo.getHomeworkName() + " " + bookGrade.getDictLabel() + clazzVo.getClassesName() + "作业讲评报告";
            AjaxResult<List<HomeworkQuestionVo>> listAjaxResult = homeworkQuestionApiService.getHomeworkQuestionListByCondition(condition);
            if (listAjaxResult.isFail()) {
                return;
            }
            List<HomeworkQuestionVo> questionList = listAjaxResult.getData();
            HomeworkResultBo resultBo = new HomeworkResultBo();
            resultBo.setClassId(condition.getClassId());
            resultBo.setHomeworkOid(condition.getHomeworkOid());
            //查询用户正确率
            AjaxResult<List<HomeworkResultVo>> homeworkResultVoAjaxResult = homeworkResultService.queryByHomeworkClassId(resultBo);
            if (homeworkResultVoAjaxResult.isFail()) {
                return;
            }
            List<HomeworkResultVo> resultList = homeworkResultVoAjaxResult.getData();

            Map<String, HomeworkResultVo> resultMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(resultList)) {
                resultMap = resultList.stream().collect(Collectors.toMap(p1 -> p1.getStuOid(), p2 -> p2, (p1, p2) -> p1));
            }
            HomeworkClassQuestionStatisticsBo statisticsBo = new HomeworkClassQuestionStatisticsBo();
            statisticsBo.setClassId(condition.getClassId());
            statisticsBo.setHomeworkOid(condition.getHomeworkOid());
            //查询题目正确率
            AjaxResult<List<HomeworkClassQuestionStatisticsVo>> ajaxResult = homeworkClassQuestionStatisticsApiService.queryByHomeworkClass(statisticsBo);
            if (ajaxResult.isFail()) {
                return;
            }
            List<HomeworkClassQuestionStatisticsVo> statisticsList = ajaxResult.getData();

            Map<String, HomeworkClassQuestionStatisticsVo> statisticsMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(statisticsList)) {
                statisticsMap = statisticsList.stream().collect(Collectors.toMap(p1 -> p1.getQuestionOid(), p2 -> p2, (p1, p2) -> p1));
            }
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            //test.xlsx是弹出下载对话框的文件名，不能为中文，中文请自行编码
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(name + ".xlsx", "UTF-8"));
            ServletOutputStream out = response.getOutputStream();
            // 设置标题样式
            StyleSet style = writer.getStyleSet();
            CellStyle headCellStyle = style.getHeadCellStyle();
            //水平居中
            headCellStyle.setAlignment(HorizontalAlignment.CENTER);
            //垂直居中
            headCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            //设置内容字体
            Font font = writer.createFont();
            //加粗
            font.setBold(true);
            //设置标题字体大小
            font.setFontHeightInPoints((short) 12);

            headCellStyle.setFont(font);
            writer.merge(0, 0, 0, questionList.size() + 2, "", headCellStyle);
            List<String> titleList1 = Arrays.asList("", "题号");
            List<String> titleList2 = Arrays.asList("姓名", "正确率");

            List<List> dateList = new ArrayList<>();
            questionList.sort(Comparator.comparing(HomeworkQuestionVo::getQuesOrderNum));
            for (HomeworkQuestionVo vo : questionList) {
                titleList1.add(vo.getQuesOrderNum().toString());
                String accuracyRate = "0";
                if (statisticsMap.containsKey(vo.getQuestionOid())) {
                    HomeworkClassQuestionStatisticsVo homeworkClassQuestionStatisticsVo = statisticsMap.get(vo.getQuestionOid());
                    if (homeworkClassQuestionStatisticsVo != null && homeworkClassQuestionStatisticsVo.getAccuracyRate() != null) {
                        accuracyRate = homeworkClassQuestionStatisticsVo.getAccuracyRate().toString();
                    }
                }
                titleList2.add(accuracyRate);
            }
            for (StudentVo stuVo : studentAllList) {
                List tmp = new ArrayList();
                tmp.add(stuVo.getUserVo().getRealName());
                String accuracyRate = "0";
                if (resultMap.containsKey(stuVo.getUserVo().getUserOid())) {
                    HomeworkResultVo homeworkResultVo = resultMap.get(stuVo.getUserVo().getUserOid());
                    if (homeworkResultVo != null && homeworkResultVo.getAccuracyRate() != null) {
                        accuracyRate = homeworkResultVo.getAccuracyRate().toString();
                    }
                }
                tmp.add(accuracyRate);
                for (HomeworkQuestionVo vo : questionList) {
                    String ans = "";
                    if (answerMap.containsKey(stuVo.getUserVo().getUserOid() + "_" + vo.getQuestionOid())) {
                        HomeworkResultAnswerVo homeworkResultAnswerVo = answerMap.get(stuVo.getUserVo().getUserOid() + "_" + vo.getQuestionOid());
                        if (homeworkResultAnswerVo != null && homeworkResultAnswerVo.getIsCorrect() != null) {
                            if (homeworkResultAnswerVo.getIsCorrect().equals(CorrectResultEnum.CORRECT.getCode())) {
                                ans = "✓";
                            } else if (homeworkResultAnswerVo.getIsCorrect().equals(CorrectResultEnum.WRONG.getCode())) {
                                ans = "✕";
                            }
                        }
                    }
                    tmp.add(ans);
                }
            }

            writer.writeHeadRow(titleList1);
            writer.writeHeadRow(titleList2);

            CellStyle cellStyle = style.getCellStyle();
            //水平居中
            cellStyle.setAlignment(HorizontalAlignment.LEFT);
            //垂直居中
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            //设置内容字体
            Font font1 = writer.createFont();
            //加粗
            font1.setBold(false);
            //设置标题字体大小
            font1.setFontHeightInPoints((short) 12);

            cellStyle.setFont(font1);
            writer.write(dateList);
            writer.flush(out);
            writer.close();
            IoUtil.close(out);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
