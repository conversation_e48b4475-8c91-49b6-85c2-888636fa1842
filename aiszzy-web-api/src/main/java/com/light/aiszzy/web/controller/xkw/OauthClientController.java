package com.light.aiszzy.web.controller.xkw;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.light.aiszzy.question.entity.bo.QuestionBo;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.StringUtils;
import com.light.security.service.CurrentUserService;
import com.light.contants.AISzzyConstants;
import com.light.aiszzy.web.utils.AesUtils;
import com.light.aiszzy.web.utils.ConfigUtils;
import com.light.aiszzy.web.utils.HttpsUtils;
import com.light.aiszzy.web.utils.SignatureUtils;
import com.light.aiszzy.xkw.xkwPaperInfo.service.XkwPaperInfoApiService;
import com.light.user.account.entity.vo.LoginAccountVo;
import com.light.user.userThirdRelationship.api.UserThirdRelationshipApi;
import com.light.user.userThirdRelationship.entity.bo.UserThirdRelationshipBo;
import com.light.user.userThirdRelationship.entity.bo.UserThirdRelationshipConditionBo;
import com.light.user.userThirdRelationship.entity.vo.UserThirdRelationshipVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;

/**
 * @Author: lwz
 * @Description: 学科网认证登录执行类
 * @date: 11:49 2018/1/10
 */
@RestController
@Api(value = "", tags = "学科网相关接口")
public class OauthClientController {

    /**
     * 学科网分配的appkey
     */
    private final String appKey;
    /**
     * 学科网分配的appSecret
     */
    private final String appSecret;
    /**
     * 学科网的服务，如http://www.zxxk.com/、http://zujuan.xkw.com/等，注意域名后面的斜杠不能少，更多服务可联系客服获取
     */
    private final String service;
    /**
     * 学科网Oauth认证平台地址
     */
    private final String oauthServerUrl;

    @Autowired
    public OauthClientController(ConfigUtils configUtils) {
        appKey = configUtils.getAppKey();
        appSecret = configUtils.getAppSecret();
        service = configUtils.getService();
        oauthServerUrl = configUtils.getOauthServerUrl();
    }

    @Resource
    private CurrentUserService currentUserService;

    @Resource
    private XkwPaperInfoApiService xkwPaperInfoApiService;

    @Resource
    private UserThirdRelationshipApi userThirdRelationshipApi;

    @RequestMapping(value = "/xwk/openId", method = {RequestMethod.POST, RequestMethod.GET})
    public AjaxResult openId(HttpServletRequest request) throws Exception {
        UserThirdRelationshipConditionBo condition = new UserThirdRelationshipConditionBo();
        condition.setUserOid(currentUserService.getCurrentOid());
        condition.setAppCode(AISzzyConstants.THIRD_USER_XKW_APP_CODE);
        condition.setIsDelete(StatusEnum.ISDELETE.getCode());
        condition.setPageNo(SystemConstants.NO_PAGE);
        AjaxResult xkwAuthUserListByCondition = userThirdRelationshipApi.getUserThirdRelationshipListByCondition(condition);
        String openId = "";
        if (xkwAuthUserListByCondition.isSuccess()) {
            Map data = (Map) xkwAuthUserListByCondition.getData();
            if (data != null && data.containsKey("list")) {
                Object list = data.get("list");
                List<UserThirdRelationshipVo> xkwAuthUserVos = JSONUtil.toList(JSONUtil.toJsonStr(list), UserThirdRelationshipVo.class);
                if (CollectionUtil.isNotEmpty(xkwAuthUserVos)) {
                    openId = xkwAuthUserVos.get(0).getThirdOid();
                }
            }
        }

        return AjaxResult.success(openId);
    }

    @ApiOperation(value = "获取跳转登录地址")
    @RequestMapping(value = "/xwk/url", method = {RequestMethod.POST, RequestMethod.GET})
    public AjaxResult code(HttpServletRequest request) throws Exception {
        String callbackUrl = request.getParameter("callbackUrl");
        if (StrUtil.isEmpty(callbackUrl)) {
            return AjaxResult.fail("参数错误");
        }

        String serviceUrl = request.getParameter("serviceUrl");

        UserThirdRelationshipConditionBo condition = new UserThirdRelationshipConditionBo();
        condition.setUserOid(currentUserService.getCurrentOid());
        condition.setAppCode(AISzzyConstants.THIRD_USER_XKW_APP_CODE);
        condition.setIsDelete(StatusEnum.ISDELETE.getCode());
        condition.setPageNo(SystemConstants.NO_PAGE);
        AjaxResult xkwAuthUserListByCondition = userThirdRelationshipApi.getUserThirdRelationshipListByCondition(condition);
        String openId = "";
        if (xkwAuthUserListByCondition.isSuccess()) {
            Map data = (Map) xkwAuthUserListByCondition.getData();
            if (data != null && data.containsKey("list")) {
                Object list = data.get("list");
                List<UserThirdRelationshipVo> xkwAuthUserVos = JSONUtil.toList(JSONUtil.toJsonStr(list), UserThirdRelationshipVo.class);
                if (CollectionUtil.isNotEmpty(xkwAuthUserVos)) {
                    openId = xkwAuthUserVos.get(0).getThirdOid();
                }
            }
        }
        String authorizeUrl = getAuthorizeUrl(openId, serviceUrl, callbackUrl, "");
        return AjaxResult.success(authorizeUrl);
    }

    /**
     * 平台认证执行器
     */
    @ApiOperation(value = "平台认证执行器，获取openId")
    @RequestMapping(value = "/xwk/authentication", method = {RequestMethod.POST, RequestMethod.GET})
    public AjaxResult processRequest(HttpServletRequest request) throws Exception {
        //从http请求中获取code
        String code = request.getParameter("code");
        String callbackUrl = request.getParameter("callbackUrl");
        if (StrUtil.isEmpty(code)) {
            return AjaxResult.fail("参数错误");
        }
        if (StrUtil.isEmpty(callbackUrl)) {
            return AjaxResult.fail("参数错误");
        }

        //code为空,跳转学科网Oauth授权中心获取code
        if (StrUtil.isEmpty(code)) {
            return AjaxResult.fail("code error");
        }
        //获取accessToken
        String accessTokenResp = getAccessToken(code, callbackUrl);
        String accessToken = getAccessTokenFromResp(accessTokenResp);
        if (StrUtil.isEmpty(accessToken)) {
            return AjaxResult.fail("accessToken failure");
        }
        LoginAccountVo loginAccountVo = currentUserService.getCurrentUser();
        String schoolCode = null;
        if (loginAccountVo.getCurrentUser() != null && loginAccountVo.getCurrentUser().getUserOrg() != null && loginAccountVo.getCurrentUser().getUserOrg().getCode() != null) {
            schoolCode = loginAccountVo.getCurrentUser().getUserOrg().getCode();
        }
        //获取openId
        String profileResp = getProfile(accessToken, schoolCode);
        String openId = getOpenIdFromResp(profileResp);
        if (StrUtil.isEmpty(openId)) {
            return AjaxResult.fail("openId failure");
        }

        /*
         * 解析xkw返回的新的openId，然后查询该用户是否已经绑定openId。如果未绑定，则添加相应的关联关系；
         * 如果已绑定，则更新最新的openId、addTime（维护用户和openId的关联关系）
         */
        maintainUserRelationShip(currentUserService.getCurrentOid(), openId);

        //未维护成功，跳转错的地址
        return AjaxResult.success(openId);
    }

    /**
     * 保存试卷
     */
    @ApiOperation(value = "保存试卷")
    @RequestMapping(value = "/xwk/savePaper", method = {RequestMethod.POST})
    public AjaxResult savePaper(@RequestBody QuestionBo bo) throws Exception {
        bo.setUserOid(currentUserService.getCurrentOid());
        bo.setCreateByRealName(currentUserService.getCurrentUser().getCurrentUser().getRealName());
        return xkwPaperInfoApiService.addXkwPaperInfo(bo);
    }

    /**
     * 更新试卷
     */
    @ApiOperation(value = "更新试卷")
    @RequestMapping(value = "/xwk/editPaper", method = {RequestMethod.POST})
    public AjaxResult editPaper(@RequestBody QuestionBo bo) throws Exception {
        bo.setUserOid(currentUserService.getCurrentOid());
        return xkwPaperInfoApiService.editXkwPaperInfo(bo);
    }

    /**
     * 获取Oauth授权地址
     *
     * @param openId      学科网返回给应用的OpenId
     * @param redirectUri 本系统的回调地址，用来处理Oauth接下来的逻辑
     * @return 授权地址
     */
    private String getAuthorizeUrl(String openId, String redirectUri) throws Exception {
        return getAuthorizeUrl(openId, null, redirectUri, "");
    }

    /**
     * 获取Oauth授权地址，除非对接时学科网有特别说明，否则不要使用该方法，您可以使用{@link OauthClientController#getAuthorizeUrl(String, String)}
     *
     * @param openId      学科网返回给应用的OpenId，非空
     * @param extra       填写手机号，邮箱，用户名中的任意一项，推荐填写顺序从高到低，对接时学科网会特别说明，非空
     * @param redirectUri 本系统的回调地址，用来处理Oauth接下来的逻辑
     * @return 授权地址
     */
    private String getAuthorizeUrl(String openId, String serviceUrl, String redirectUri, String extra) throws Exception {
        openId = AesUtils.aesEncrypt(openId, appSecret);
        String timeSpan = AesUtils.aesEncrypt(getTimeStamp(), appSecret);
        if (StrUtil.isEmpty(extra)) {
            extra = "";
        } else {
            extra = AesUtils.aesEncrypt(extra, appSecret);
        }
        String url = oauthServerUrl + "/oauth2/authorize";
        //此处必须使用SortedMap，主要是利用SortedMap能够根据参数名首字母自动进行排序的特性
        SortedMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("client_id", appKey);
        paramMap.put("open_id", openId);
        if (StringUtils.isEmpty(serviceUrl)) {
            serviceUrl = service;
        }
        paramMap.put("service", serviceUrl);
        paramMap.put("redirect_uri", redirectUri);
        paramMap.put("timespan", timeSpan);
        paramMap.put("extra", extra);
        paramMap.put("signature", SignatureUtils.generateSignature(paramMap, appSecret));
        //加密之后参数中会含有特殊字符，如加号“+”，会导致浏览器url解析成空格，因此需要给参数值编一下码
        return url + "?" + getEncodedParamStr(paramMap);
    }

    /**
     * 内部方法：获取accessToken认证
     *
     * @param code        非空
     * @param redirectUri 回调地址，非空
     * @return 获取到的json字符串，内包含accessToken信息
     */
    private String getAccessToken(String code, String redirectUri) throws Exception {
        //此处必须使用SortedMap，主要是利用SortedMap能够根据参数名首字母自动进行排序的特性
        SortedMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("client_id", appKey);
        paramMap.put("code", code);
        paramMap.put("redirect_uri", redirectUri);
        paramMap.put("signature", SignatureUtils.generateSignature(paramMap, appSecret));
        String url = oauthServerUrl + "/oauth2/accessToken?" + getEncodedParamStr(paramMap);
        return HttpsUtils.httpPost(url);
    }

    /**
     * 内部方法：获取OpenId
     *
     * @param accessToken 非空
     * @return 获取到的json字符串，内包含OpenId信息
     */
    private String getProfile(String accessToken) throws Exception {
        String url = String.format(oauthServerUrl + "/oauth2/profile?access_token=%s", accessToken);
        return HttpsUtils.httpPost(url);
    }

    /**
     * 内部方法：获取OpenId，除非对接时学科网有特别说明，否则不要使用该方法，您可使用{@link OauthClientController#getProfile(String)}
     *
     * @param accessToken 非空
     * @param schoolId    由学科网分配，对接时学科网会特别说明该参数，非空
     * @return 获取到的json字符串，内包含OpenId信息
     */
    private String getProfile(String accessToken, String schoolId) throws Exception {
        if (StrUtil.isEmpty(schoolId)) {
            return getProfile(accessToken);
        }
        String url = String.format(oauthServerUrl + "/oauth2/profile?access_token=%s&schoolId=%s", accessToken, schoolId);
        return HttpsUtils.httpPost(url);
    }

    /**
     * 获取accessToken
     *
     * @param strResp respone响应字符串
     */
    private String getAccessTokenFromResp(String strResp) {
        return getFiledFormResp(strResp, "access_token");
    }

    /**
     * 获取openId
     *
     * @param strResp respone响应字符串
     */
    private String getOpenIdFromResp(String strResp) {
        return getFiledFormResp(strResp, "open_id");
    }

    /**
     * 获取error信息
     *
     * @param strResp respone响应字符串
     */
    private String getErrorFromResp(String strResp) {
        return getFiledFormResp(strResp, "error");
    }

    /**
     * 内部方法：从xkw返回数据中解析指定字段的数据值
     *
     * @param strResp respone响应字符串
     * @param filed   指定字段
     * @return 指定字段的值
     */
    private String getFiledFormResp(String strResp, String filed) {
        if (StrUtil.isEmpty(strResp) || StrUtil.isEmpty(filed)) {
            return "";
        }
        //将json数据转为map格式进行维护
        JSONObject jasonObject = JSONUtil.parseObj(strResp);
        return (String) jasonObject.get(filed);
    }

    /**
     * 维护openId与userId的关联关系;在存储对应关系时，第三方应用需判断当前用户是否已经绑定openId，
     * 如果已经绑定则进行更新，如果未绑定则进行插入；
     */
    private boolean maintainUserRelationShip(String userId, String openId) {
        if (StrUtil.isEmpty(openId) || StrUtil.isEmpty(userId)) {
            return false;
        }

        UserThirdRelationshipBo condition = new UserThirdRelationshipBo();
        condition.setUserOid(userId);
        condition.setThirdOid(openId);
        condition.setSourceType(AISzzyConstants.THIRD_USER_SOURCE_INTEFACE);
        condition.setAppCode(AISzzyConstants.THIRD_USER_XKW_APP_CODE);
        AjaxResult ajaxResult = userThirdRelationshipApi.addOrUpdateThirdRelationship(condition);

        return ajaxResult.isSuccess();
    }

    /**
     * 内部方法：动态获取项目的回调地址
     */
    private static String getRedirectUrl(HttpServletRequest request) {
        String url = "";
        if (request.getServerPort() == 80) {
            url = String.format("%s://%s%s/authentication", request.getScheme(), request.getServerName(), request.getContextPath());
            return url;
        }
        url = String.format("%s://%s:%s%s/authentication", request.getScheme(), request.getServerName(), request.getServerPort(), request.getContextPath());
        return url;
    }

    /**
     * 内部方法：获取时间戳
     */
    private String getTimeStamp() {
        return String.valueOf(System.currentTimeMillis());
    }

    /**
     * 将所有的参数值进行编码，防止参数值中会参数特殊字符，如加号"+"，导致浏览器url解析成空格，因此需要给参数值编一下码
     *
     * @param paramMap 参数Map
     * @return
     * @throws UnsupportedEncodingException
     */
    private String getEncodedParamStr(Map<String, Object> paramMap) throws UnsupportedEncodingException {
        StringBuilder encodedParamStr = new StringBuilder();
        for (Map.Entry param : paramMap.entrySet()) {
            if (encodedParamStr.length() > 0) {
                encodedParamStr.append("&");
            }
            encodedParamStr.append(param.getKey()).append("=").append(URLEncoder.encode(param.getValue().toString(), "utf-8"));
        }
        return encodedParamStr.toString();
    }
}
