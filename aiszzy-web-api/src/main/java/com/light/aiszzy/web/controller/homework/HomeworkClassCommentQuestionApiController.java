package com.light.aiszzy.web.controller.homework;

import com.github.pagehelper.PageInfo;
import com.light.aiszzy.homework.entity.bo.HomeworkClassCommentQuestionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkClassCommentQuestionConditionBo;
import com.light.aiszzy.homework.entity.vo.HomeworkClassCommentQuestionVo;
import com.light.aiszzy.homework.service.HomeworkClassCommentQuestionApiService;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import com.light.security.service.CurrentUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 班级作业讲评题目
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 10:39:09
 */
@RestController
@Validated
@Api(value = "", tags = "班级作业讲评题目接口" )
public class HomeworkClassCommentQuestionApiController {

	@Resource
	private CurrentUserService currentUserService;

    @Autowired
    private HomeworkClassCommentQuestionApiService homeworkClassCommentQuestionApiService;

	@PostMapping("/homeworkClassCommentQuestion/pageList")
	@ApiOperation(value = "分页查询班级作业讲评题目",httpMethod = "POST")
	public AjaxResult<PageInfo<HomeworkClassCommentQuestionVo>> getHomeworkClassCommentQuestionPageListByCondition(@RequestBody HomeworkClassCommentQuestionConditionBo condition){
		condition.setOrgCode(currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getCode());
		condition.setCreateBy(currentUserService.getCurrentOid());
		return homeworkClassCommentQuestionApiService.getHomeworkClassCommentQuestionPageListByCondition(condition);
    }

	@PostMapping("/homeworkClassCommentQuestion/list")
	@ApiOperation(value = "查询所有班级作业讲评题目",httpMethod = "POST")
	public AjaxResult<List<HomeworkClassCommentQuestionVo>> getHomeworkClassCommentQuestionAllListByCondition(@RequestBody HomeworkClassCommentQuestionConditionBo condition){
		condition.setPageNo(SystemConstants.NO_PAGE);
		return homeworkClassCommentQuestionApiService.getHomeworkClassCommentQuestionListByCondition(condition);
	}

	@PostMapping("/homeworkClassCommentQuestion/add")
	@ApiOperation(value = "新增班级作业讲评题目",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "新增班级作业讲评题目", operationType = OperationLogConstants.OP_TERM_APP, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult addHomeworkClassCommentQuestion(@Validated @RequestBody HomeworkClassCommentQuestionBo homeworkClassCommentQuestionBo){
		homeworkClassCommentQuestionBo.setOrgCode(currentUserService.getCurrentUser().getCurrentUser().getUserOrg().getCode());
		homeworkClassCommentQuestionBo.setCreateBy(currentUserService.getCurrentOid());
		return homeworkClassCommentQuestionApiService.addHomeworkClassCommentQuestion(homeworkClassCommentQuestionBo);
    }

	@PostMapping("/homeworkClassCommentQuestion/update")
	@ApiOperation(value = "修改班级作业讲评题目",httpMethod = "POST")
	@OperationLogAnnotation(moduleName = "修改班级作业讲评题目", operationType = OperationLogConstants.OP_TYPE_UPDATE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult updateHomeworkClassCommentQuestion(@Validated @RequestBody HomeworkClassCommentQuestionBo homeworkClassCommentQuestionBo) {
		return homeworkClassCommentQuestionApiService.updateHomeworkClassCommentQuestion(homeworkClassCommentQuestionBo);
	}

	@GetMapping("/homeworkClassCommentQuestion/detail")
	@ApiOperation(value = "查询班级作业讲评题目详情",httpMethod = "GET")
	@ApiImplicitParam(name = "homeworkClassCommentQuestionId", value = "oid", required = true, dataType = "String", paramType = "query")
	public AjaxResult<HomeworkClassCommentQuestionVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid) {
		return homeworkClassCommentQuestionApiService.getDetail(oid);
	}

	@GetMapping("/homeworkClassCommentQuestion/delete")
	@ApiOperation(value = "删除班级作业讲评题目",httpMethod = "GET")
	@ApiImplicitParam(name = "oid", value = "oid", required = true, dataType = "String", paramType = "delete")
	@OperationLogAnnotation(moduleName = "删除班级作业讲评题目", operationType = OperationLogConstants.OP_TYPE_DELETE, operationTerminal = OperationLogConstants.OP_TERM_DESKTOP)
	public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid) {
		return homeworkClassCommentQuestionApiService.delete(oid);
	}
}
