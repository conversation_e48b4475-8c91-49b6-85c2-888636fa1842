package com.light.aiszzy.statistics.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.light.aiszzy.statistics.entity.dto.SchoolGradeQuestionStatisticsDto;
import com.light.aiszzy.statistics.mapper.SchoolGradeQuestionStatisticsMapper;
import com.light.aiszzy.statistics.entity.bo.SchoolGradeQuestionStatisticsBo;
import com.light.aiszzy.statistics.entity.bo.SchoolGradeQuestionStatisticsConditionBo;
import com.light.aiszzy.statistics.entity.vo.SchoolGradeQuestionStatisticsVo;
import com.light.aiszzy.statistics.service.ISchoolGradeQuestionStatisticsService;
import com.light.core.constants.SystemConstants;
import com.light.core.utils.StringUtils;
import com.light.enums.SzzyRedisKeyEnum;
import com.light.redis.component.RedisComponent;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.core.entity.AjaxResult;
/**
 * 作业年级题目正确率接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 10:39:09
 */
@Service
public class SchoolGradeQuestionStatisticsServiceImpl extends ServiceImpl<SchoolGradeQuestionStatisticsMapper, SchoolGradeQuestionStatisticsDto> implements ISchoolGradeQuestionStatisticsService {

	@Resource
	private SchoolGradeQuestionStatisticsMapper schoolGradeQuestionStatisticsMapper;

	@Resource
	private RedisComponent redisComponent;
	
    @Override
	public List<SchoolGradeQuestionStatisticsVo> getSchoolGradeQuestionStatisticsListByCondition(SchoolGradeQuestionStatisticsConditionBo condition) {
        return schoolGradeQuestionStatisticsMapper.getSchoolGradeQuestionStatisticsListByCondition(condition);
	}

	@Override
	public AjaxResult addSchoolGradeQuestionStatistics(SchoolGradeQuestionStatisticsBo schoolGradeQuestionStatisticsBo) {
		SchoolGradeQuestionStatisticsDto schoolGradeQuestionStatistics = new SchoolGradeQuestionStatisticsDto();
		BeanUtils.copyProperties(schoolGradeQuestionStatisticsBo, schoolGradeQuestionStatistics);
		schoolGradeQuestionStatistics.setIsDelete(StatusEnum.NOTDELETE.getCode());
		schoolGradeQuestionStatistics.setOid(IdUtil.simpleUUID());
		if(save(schoolGradeQuestionStatistics)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateSchoolGradeQuestionStatistics(SchoolGradeQuestionStatisticsBo schoolGradeQuestionStatisticsBo) {
		LambdaQueryWrapper<SchoolGradeQuestionStatisticsDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(SchoolGradeQuestionStatisticsDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(SchoolGradeQuestionStatisticsDto::getOid, schoolGradeQuestionStatisticsBo.getOid());
		SchoolGradeQuestionStatisticsDto schoolGradeQuestionStatistics = getOne(lqw);
		Long id = schoolGradeQuestionStatistics.getId();
		if(schoolGradeQuestionStatistics == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(schoolGradeQuestionStatisticsBo, schoolGradeQuestionStatistics);
		schoolGradeQuestionStatistics.setId(id);
		if(updateById(schoolGradeQuestionStatistics)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public SchoolGradeQuestionStatisticsVo getDetail(String oid) {
		LambdaQueryWrapper<SchoolGradeQuestionStatisticsDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(SchoolGradeQuestionStatisticsDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(SchoolGradeQuestionStatisticsDto::getOid, oid);
		SchoolGradeQuestionStatisticsDto schoolGradeQuestionStatistics = getOne(lqw);
	    SchoolGradeQuestionStatisticsVo schoolGradeQuestionStatisticsVo = new SchoolGradeQuestionStatisticsVo();
		if(schoolGradeQuestionStatistics != null){
			BeanUtils.copyProperties(schoolGradeQuestionStatistics, schoolGradeQuestionStatisticsVo);
		}
		return schoolGradeQuestionStatisticsVo;
	}

	@Override
	public SchoolGradeQuestionStatisticsVo getDetailBySchoolGradeQuestion(String orgCode, Integer grade, String questionOid) {

		//优先从Redis中取值
		String  redisKey = SzzyRedisKeyEnum.ORG_CODE_GRADE_QUESTION_STATISTICS.getValue() + orgCode + SystemConstants.SEPERATOR_COLON + grade + SystemConstants.SEPERATOR_COLON + questionOid + SystemConstants.SEPERATOR_COLON;
		Object object = redisComponent.get(redisKey);
		if (null != object) {
			SchoolGradeQuestionStatisticsVo schoolGradeQuestionStatisticsVo = JSON.parseObject(JSONUtil.toJsonStr(object), SchoolGradeQuestionStatisticsVo.class);

			if (schoolGradeQuestionStatisticsVo != null && StringUtils.isNotBlank(schoolGradeQuestionStatisticsVo.getOrgCode())) {
				return schoolGradeQuestionStatisticsVo;
			}
		}

		LambdaQueryWrapper<SchoolGradeQuestionStatisticsDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(SchoolGradeQuestionStatisticsDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(SchoolGradeQuestionStatisticsDto::getOrgCode, orgCode);
		lqw.eq(SchoolGradeQuestionStatisticsDto::getGrade, grade);
		lqw.eq(SchoolGradeQuestionStatisticsDto::getQuestionOid, questionOid);
		SchoolGradeQuestionStatisticsDto schoolGradeQuestionStatistics = getOne(lqw);
		SchoolGradeQuestionStatisticsVo schoolGradeQuestionStatisticsVo = new SchoolGradeQuestionStatisticsVo();
		if(schoolGradeQuestionStatistics != null){
			BeanUtils.copyProperties(schoolGradeQuestionStatistics, schoolGradeQuestionStatisticsVo);
			//同步入缓存
			redisComponent.set(redisKey, JSON.toJSONString(schoolGradeQuestionStatisticsVo),3600 * 24);
		}
		return schoolGradeQuestionStatisticsVo;
	}

}