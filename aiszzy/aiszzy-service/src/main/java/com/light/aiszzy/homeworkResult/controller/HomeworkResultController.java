package com.light.aiszzy.homeworkResult.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;

import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultConditionBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultBo;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultVo;
import com.light.aiszzy.homeworkResult.service.IHomeworkResultService;

import com.light.aiszzy.homeworkResult.api.HomeworkResultApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import com.light.core.constants.SystemConstants;

/**
 * 校本作业结果表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@RestController
@Validated
@Api(value = "", tags = "校本作业结果表接口")
public class HomeworkResultController implements HomeworkResultApi {

    @Autowired
    private IHomeworkResultService homeworkResultService;

    public AjaxResult<PageInfo<HomeworkResultVo>> getHomeworkResultPageListByCondition(@RequestBody HomeworkResultConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(),condition.getClass()));
        PageInfo<HomeworkResultVo> pageInfo = new PageInfo<>(homeworkResultService.getHomeworkResultListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<HomeworkResultVo>> getHomeworkResultListByCondition(@RequestBody HomeworkResultConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(homeworkResultService.getHomeworkResultListByCondition(condition));
    }

    @Override
    public AjaxResult<List<HomeworkResultVo>> getDuplicateHomeworkResultListByCondition(HomeworkResultConditionBo condition) {
        if (null == condition.getHomeworkOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(homeworkResultService.getDuplicateHomeworkResultListByCondition(condition));
    }

    public AjaxResult addHomeworkResult(@Validated @RequestBody HomeworkResultBo homeworkResultBo) {
        return homeworkResultService.addHomeworkResult(homeworkResultBo);
    }

    public AjaxResult updateHomeworkResult(@Validated @RequestBody HomeworkResultBo homeworkResultBo) {
        if (null == homeworkResultBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return homeworkResultService.updateHomeworkResult(homeworkResultBo);
    }

    public AjaxResult<HomeworkResultVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(homeworkResultService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            HomeworkResultBo homeworkResultBo = new HomeworkResultBo();
            homeworkResultBo.setOid(oid);
            homeworkResultBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return homeworkResultService.updateHomeworkResult(homeworkResultBo);
    }

    @Override
    public AjaxResult<List<HomeworkResultVo>> queryByHomeworkClassId(HomeworkResultBo homeworkResultBo) {
        return AjaxResult.success(homeworkResultService.queryByHomeworkClassId(homeworkResultBo.getHomeworkOid(),homeworkResultBo.getClassId()));
    }
}
