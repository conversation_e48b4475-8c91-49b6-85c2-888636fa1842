package com.light.aiszzy.statistics.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 题目正确率
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-08-02 18:11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("question_statistics")
public class QuestionStatisticsDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 关联question题目oid
	 */
	@TableField("question_oid")
	private String questionOid;

	/**
	 * 题型id
	 */
	@TableField("question_type_id")
	private String questionTypeId;

	/**
	 * 题型名称
	 */
	@TableField("question_type_name")
	private String questionTypeName;

	/**
	 * 学科code
	 */
	@TableField("subject")
	private Integer subject;

	/**
	 * 知识点,多个逗号分割
	 */
	@TableField("knowledge_points_id")
	private String knowledgePointsId;

	/**
	 * 章节ID
	 */
	@TableField("chapter_id")
	private String chapterId;

	/**
	 * 节ID
	 */
	@TableField("section_id")
	private String sectionId;

	/**
	 * 正确个数
	 */
	@TableField("right_num")
	private Integer rightNum;

	/**
	 * 错误个数
	 */
	@TableField("wrong_num")
	private Integer wrongNum;

	/**
	 * 提交个数
	 */
	@TableField("total_num")
	private Integer totalNum;

	/**
	 * 题目正确率小数*100
	 */
	@TableField("accuracy_rate")
	private Integer accuracyRate;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新者
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
