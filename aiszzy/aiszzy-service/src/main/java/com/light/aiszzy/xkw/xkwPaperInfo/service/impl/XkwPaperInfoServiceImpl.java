package com.light.aiszzy.xkw.xkwPaperInfo.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.light.aiszzy.apiRequestLog.entity.dto.ApiRequestLogDto;
import com.light.aiszzy.homework.entity.dto.HomeworkDto;
import com.light.aiszzy.homework.entity.dto.HomeworkQuestionDto;
import com.light.aiszzy.homework.entity.vo.HomeworkQuestionVo;
import com.light.aiszzy.homework.service.IHomeworkQuestionService;
import com.light.aiszzy.homework.service.IHomeworkService;
import com.light.aiszzy.question.entity.bo.QuestionBo;
import com.light.aiszzy.question.entity.dto.QuestionDto;
import com.light.aiszzy.question.service.IQuestionService;
import com.light.aiszzy.resourcesQuestion.entity.dto.ResourcesQuestionDto;
import com.light.aiszzy.resourcesQuestion.service.IResourcesQuestionService;
import com.light.aiszzy.xkw.xkwCourses.entity.dto.XkwCoursesDto;
import com.light.aiszzy.xkw.xkwCourses.mapper.XkwCoursesMapper;
import com.light.aiszzy.xkw.xkwPaperInfo.entity.bo.ExamPaper;
import com.light.aiszzy.xkw.xkwQuestionTypes.entity.dto.XkwQuestionTypesDto;
import com.light.aiszzy.xkw.xkwQuestionTypes.mapper.XkwQuestionTypesMapper;
import com.light.contants.AISzzyConstants;
import com.light.core.constants.SystemConstants;
import com.light.core.utils.StringUtils;
import com.light.user.userThirdRelationship.api.UserThirdRelationshipApi;
import com.light.user.userThirdRelationship.entity.bo.UserThirdRelationshipConditionBo;
import com.light.user.userThirdRelationship.entity.vo.UserThirdRelationshipVo;
import com.light.utils.ApiRequestLogUtil;
import com.xkw.xop.client.XopHttpClient;
import kong.unirest.HttpResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.xkw.xkwPaperInfo.entity.dto.XkwPaperInfoDto;
import com.light.aiszzy.xkw.xkwPaperInfo.entity.bo.XkwPaperInfoConditionBo;
import com.light.aiszzy.xkw.xkwPaperInfo.entity.bo.XkwPaperInfoBo;
import com.light.aiszzy.xkw.xkwPaperInfo.entity.vo.XkwPaperInfoVo;
import com.light.aiszzy.xkw.xkwPaperInfo.service.IXkwPaperInfoService;
import com.light.aiszzy.xkw.xkwPaperInfo.mapper.XkwPaperInfoMapper;
import com.light.core.entity.AjaxResult;

/**
 * 题目信息接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-08 09:40:43
 */
@Slf4j
@Service
public class XkwPaperInfoServiceImpl extends ServiceImpl<XkwPaperInfoMapper, XkwPaperInfoDto> implements IXkwPaperInfoService {

    private static final String XKW_QUESTION_URL = "/xopqbm/zj-saas/v2/users/download-papers/details";
    //    private static final String XKW_QUESTION_URL = "/xopqbm/zj-saas/users/download-papers/details";
    @Resource
    private XkwPaperInfoMapper xkwPaperInfoMapper;

    @Resource
    private XkwCoursesMapper xkwCoursesMapper;

    @Resource
    private XkwQuestionTypesMapper XkwQuestionTypesMapper;

    @Resource
    private XopHttpClient xopHttpClient;

    @Resource
    private UserThirdRelationshipApi userThirdRelationshipApi;

    @Resource
    private IResourcesQuestionService resourcesQuestionService;

    @Resource
    private IQuestionService questionService;

    @Resource
    private IHomeworkService homeworkService;

    @Resource
    private IHomeworkQuestionService homeworkQuestionService;


    @Resource(name = "taskExecutor")
    private TaskExecutor taskExecutor;

    @Override
    public List<XkwPaperInfoVo> getXkwPaperInfoListByCondition(XkwPaperInfoConditionBo condition) {
        return xkwPaperInfoMapper.getXkwPaperInfoListByCondition(condition);
    }

    @Override
    public AjaxResult addXkwPaperInfo(QuestionBo bo) {
        UserThirdRelationshipConditionBo condition = new UserThirdRelationshipConditionBo();
        condition.setUserOid(bo.getUserOid());
        condition.setAppCode(AISzzyConstants.THIRD_USER_XKW_APP_CODE);
        condition.setIsDelete(StatusEnum.ISDELETE.getCode());
        condition.setPageNo(SystemConstants.NO_PAGE);
        AjaxResult xkwAuthUserListByCondition = userThirdRelationshipApi.getUserThirdRelationshipListByCondition(condition);
        String openId = "";
        if (xkwAuthUserListByCondition.isSuccess()) {
            Map data = (Map) xkwAuthUserListByCondition.getData();
            if (data != null && data.containsKey("list")) {
                Object list = data.get("list");
                List<UserThirdRelationshipVo> xkwAuthUserVos = JSONUtil.toList(JSONUtil.toJsonStr(list), UserThirdRelationshipVo.class);
                if (CollectionUtil.isNotEmpty(xkwAuthUserVos)) {
                    openId = xkwAuthUserVos.get(0).getThirdOid();
                }
            }
        }
        if (StringUtils.isEmpty(openId)) {
            return AjaxResult.fail("获取用户信息失败");
        }
        Map<String, Object> params = new HashMap<>();
        params.put("id", bo.getPaperId());
        params.put("user_id", openId);
        HttpResponse<String> response = xopHttpClient.get(XKW_QUESTION_URL, params);
        String body = response.getBody();
        log.info("======body===={}", body);
        JSONObject jsonObject = JSONUtil.parseObj(body);

        String code = jsonObject.getStr("code");
        ApiRequestLogDto apiRequestLogDto = new ApiRequestLogDto();
        apiRequestLogDto.setMethod("get");
        apiRequestLogDto.setUrl(XKW_QUESTION_URL);
        apiRequestLogDto.setParams(JSONUtil.toJsonStr(params));
        apiRequestLogDto.setResponse(body);
        ApiRequestLogUtil.publishMessage(JSONUtil.toJsonStr(apiRequestLogDto));

        if (!"2000000".equals(code)) {
            return AjaxResult.fail("接口获取失败");
        }
        String data = jsonObject.getStr("data");
        ExamPaper bean = JSONUtil.toBean(data, ExamPaper.class);
        taskExecutor.execute(() -> saveQuestion(bean, bo.getUserOid(), bo.getPaperId()));
        HomeworkDto homeworkDto = dealXKWQuestion(bo, bean);
        return AjaxResult.success(homeworkDto);
    }

    @Override
    public AjaxResult editXkwPaperInfo(QuestionBo bo) {
        UserThirdRelationshipConditionBo condition = new UserThirdRelationshipConditionBo();
        condition.setUserOid(bo.getUserOid());
        condition.setAppCode(AISzzyConstants.THIRD_USER_XKW_APP_CODE);
        condition.setIsDelete(StatusEnum.ISDELETE.getCode());
        condition.setPageNo(SystemConstants.NO_PAGE);
        AjaxResult xkwAuthUserListByCondition = userThirdRelationshipApi.getUserThirdRelationshipListByCondition(condition);
        String openId = "";
        if (xkwAuthUserListByCondition.isSuccess()) {
            Map data = (Map) xkwAuthUserListByCondition.getData();
            if (data != null && data.containsKey("list")) {
                Object list = data.get("list");
                List<UserThirdRelationshipVo> xkwAuthUserVos = JSONUtil.toList(JSONUtil.toJsonStr(list), UserThirdRelationshipVo.class);
                if (CollectionUtil.isNotEmpty(xkwAuthUserVos)) {
                    openId = xkwAuthUserVos.get(0).getThirdOid();
                }
            }
        }
        if (StringUtils.isEmpty(openId)) {
            return AjaxResult.fail("获取用户信息失败");
        }
        Map<String, Object> params = new HashMap<>();
        params.put("id", bo.getPaperId());
        params.put("user_id", openId);
        HttpResponse<String> response = xopHttpClient.get(XKW_QUESTION_URL, params);
        String body = response.getBody();
        log.info("======body===={}", body);
        JSONObject jsonObject = JSONUtil.parseObj(body);

        String code = jsonObject.getStr("code");
        ApiRequestLogDto apiRequestLogDto = new ApiRequestLogDto();
        apiRequestLogDto.setMethod("get");
        apiRequestLogDto.setUrl(XKW_QUESTION_URL);
        apiRequestLogDto.setParams(JSONUtil.toJsonStr(params));
        apiRequestLogDto.setResponse(body);
        ApiRequestLogUtil.publishMessage(JSONUtil.toJsonStr(apiRequestLogDto));

        if (!"2000000".equals(code)) {
            return AjaxResult.fail("接口获取失败");
        }
        String data = jsonObject.getStr("data");
        ExamPaper bean = JSONUtil.toBean(data, ExamPaper.class);
        taskExecutor.execute(() -> saveQuestion(bean, bo.getUserOid(), bo.getPaperId()));
        HomeworkDto homeworkDto = dealXKWQuestion(bo, bean);
        return AjaxResult.success(homeworkDto);
    }

    public HomeworkDto dealXKWQuestion(QuestionBo bo, ExamPaper bean) {
        List<ExamPaper.Body> body = bean.getBody();
        List<QuestionDto> questionArr = new ArrayList<>();
        int courseId = bean.getCourse_id();
        XkwCoursesDto xkwCoursesDto = xkwCoursesMapper.selectById(courseId);
        List<ResourcesQuestionDto> resQuestionArr = new ArrayList<>();
        Map<String, ResourcesQuestionDto> existResQuesMap = new HashMap<>();
        Map<String, QuestionDto> existQuesMap = new HashMap<>();
        for (ExamPaper.Body bodyItem : body) {
            List<ExamPaper.PartBody> partBody = bodyItem.getPart_body();

            if (CollectionUtil.isNotEmpty(partBody)) {
                List<Long> collect = partBody.stream().flatMap(o -> o.getQuestions().stream()).map(ExamPaper.Question::getId).collect(Collectors.toList());

                if (CollectionUtil.isNotEmpty(collect)) {
                    List<ResourcesQuestionDto> existResQues = resourcesQuestionService.list(new LambdaQueryWrapper<ResourcesQuestionDto>()
                            .eq(ResourcesQuestionDto::getIsDelete, StatusEnum.ISDELETE.getCode())
                            .eq(ResourcesQuestionDto::getThirdSourceType, AISzzyConstants.THIRD_USER_XKW_APP_CODE)
                            .in(ResourcesQuestionDto::getId, collect));
                    if (CollectionUtil.isNotEmpty(existResQues)) {
                        existResQuesMap = existResQues.stream().collect(Collectors.toMap(p1 -> p1.getThirdOutId(), p2 -> p2, (p1, p2) -> p1));
                    }

                    List<QuestionDto> existQues = questionService.list(new LambdaQueryWrapper<QuestionDto>()
                            .in(QuestionDto::getId, collect)
                            .eq(QuestionDto::getThirdSourceType, AISzzyConstants.THIRD_USER_XKW_APP_CODE)
                            .eq(QuestionDto::getIsDelete, StatusEnum.ISDELETE.getCode()));
                    if (CollectionUtil.isNotEmpty(existQues)) {
                        existQuesMap = existQues.stream().collect(Collectors.toMap(p1 -> p1.getThirdOutId(), p2 -> p2, (p1, p2) -> p1));
                    }
                }

            }
            for (ExamPaper.PartBody partBodyItem : partBody) {
                List<ExamPaper.Question> questions = partBodyItem.getQuestions();
                for (ExamPaper.Question questionItem : questions) {
                    QuestionDto quesDto = new QuestionDto();
                    ResourcesQuestionDto resQuestionDto = new ResourcesQuestionDto();
                    resQuestionDto.setOid(IdUtil.simpleUUID());
                    resQuestionDto.setQuesBody(questionItem.getStem());
                    resQuestionDto.setQuesBodyType(AISzzyConstants.QuestionShowType.HTML.getType());
                    resQuestionDto.setQuesAnswer(questionItem.getAnswer());
                    resQuestionDto.setQuesAnswerType(AISzzyConstants.QuestionShowType.HTML.getType());
                    resQuestionDto.setAnalysisAnswer(questionItem.getExplanation());
                    resQuestionDto.setAnalysisAnswerType(AISzzyConstants.QuestionShowType.HTML.getType());
                    resQuestionDto.setThirdSourceType(AISzzyConstants.THIRD_USER_XKW_APP_CODE);
                    resQuestionDto.setThirdOutId(String.valueOf(questionItem.getId()));
                    resQuestionDto.setQuestionTypeId(questionItem.getType_id());
                    if (questionItem.getType() != null && questionItem.getType().getName() != null) {
                        resQuestionDto.setQuestionTypeName(questionItem.getType().getName());
                    } else {
                        XkwQuestionTypesDto xkwQuestionTypesDto = XkwQuestionTypesMapper.selectById(resQuestionDto.getQuestionTypeId());
                        resQuestionDto.setQuestionTypeName(xkwQuestionTypesDto.getName());
                    }
                    if (CollectionUtil.isNotEmpty(questionItem.getKpoint_ids())) {
                        resQuestionDto.setKnowledgePointsId(String.join(",", questionItem.getKpoint_ids()));
                    }
                    if (CollectionUtil.isNotEmpty(questionItem.getCatalog_ids())) {
                        resQuestionDto.setChapterId(String.join(",", questionItem.getCatalog_ids()));
                    }

                    if (CollectionUtil.isNotEmpty(questionItem.getYears())) {
                        resQuestionDto.setYear(String.join(",", questionItem.getYears()));
                    }

                    if (StringUtils.isNotEmpty(questionItem.getDifficulty_level())) {
                        JSONObject diffLevel = JSONUtil.parseObj(questionItem.getDifficulty_level());
                        resQuestionDto.setDifficultId(diffLevel.getInt("id"));
                    }
                    resQuestionDto.setSubject(bo.getSubject());
                    resQuestionDto.setGrade(bo.getGrade());
                    resQuestionDto.setQuestionJson(JSONUtil.toJsonStr(questionItem));

                    BeanUtils.copyProperties(resQuestionDto, quesDto);
                    quesDto.setOid(IdUtil.simpleUUID());
                    quesDto.setInsideLinkOid(resQuestionDto.getOid());
                    quesDto.setInsideSourceType(AISzzyConstants.InsideSourceType.RESOURCE_QUESTION.getType());
                    resQuestionArr.add(resQuestionDto);
                    questionArr.add(quesDto);
                }
            }
        }

        if (CollectionUtil.isNotEmpty(resQuestionArr)) {
            List<ResourcesQuestionDto> resQuestionTmp = new ArrayList<>();
            for (ResourcesQuestionDto dto : resQuestionArr) {
                if (existResQuesMap.containsKey(dto.getThirdOutId())) {
                    dto.setOid(existResQuesMap.get(dto.getThirdOutId()).getOid());
                } else {
                    resQuestionTmp.add(dto);
                }
            }
            if (CollectionUtil.isNotEmpty(resQuestionTmp)) {
                resourcesQuestionService.saveBatch(resQuestionTmp);
            }
        }
        if (CollectionUtil.isNotEmpty(questionArr)) {
            List<QuestionDto> questionTmp = new ArrayList<>();
            for (QuestionDto dto : questionArr) {
                if (existQuesMap.containsKey(dto.getThirdOutId())) {
                    dto.setOid(existQuesMap.get(dto.getThirdOutId()).getOid());
                } else {
                    questionTmp.add(dto);
                }
            }
            if (CollectionUtil.isNotEmpty(questionTmp)) {
                questionService.saveBatch(questionTmp);
            }
        }
        HomeworkDto homeworkDto = new HomeworkDto();

        Map<String, HomeworkQuestionDto> existHomeworkQuestionMap = new HashMap<>();
        if (StringUtils.isNotEmpty(bo.getHomeworkOid())) {
            LambdaQueryWrapper<HomeworkDto> lqw = new LambdaQueryWrapper<>();
            lqw.eq(HomeworkDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
            lqw.eq(HomeworkDto::getOid, bo.getHomeworkOid());
            homeworkDto = homeworkService.getOne(lqw);

            List<HomeworkQuestionDto> homeworkQuestionList = homeworkQuestionService.list(new LambdaQueryWrapper<HomeworkQuestionDto>()
                    .eq(HomeworkQuestionDto::getHomeworkOid, homeworkDto.getOid())
                    .eq(HomeworkQuestionDto::getIsDelete, StatusEnum.NOTDELETE.getCode()));
            if (CollectionUtil.isNotEmpty(homeworkQuestionList)) {
                existHomeworkQuestionMap = homeworkQuestionList.stream().collect(Collectors.toMap(p1 -> p1.getQuestionNo(), p2 -> p2, (p1, p2) -> p1));
            }
        }
        if (CollectionUtil.isNotEmpty(questionArr)) {
            if (homeworkDto.getId() == null) {
                BeanUtils.copyProperties(bo, homeworkDto);
                homeworkDto.setOid(IdUtil.simpleUUID());
                homeworkDto.setGenerateRuleType(AISzzyConstants.HomeWorkGenerateRuleType.ORDINARY.getCode());
                homeworkDto.setSourceType(AISzzyConstants.HomeWorkSourceType.ORDINARY.getCode());
                homeworkDto.setSubject(xkwCoursesDto.getSubjectId().intValue());
                homeworkDto.setGenerateRuleType(AISzzyConstants.HomeWorkGenerateRuleType.ORDINARY.getCode());
                homeworkDto.setSourceType(AISzzyConstants.HomeWorkSourceType.ORDINARY.getCode());
                homeworkDto.setCreateByRealName(bo.getCreateByRealName());
                homeworkService.save(homeworkDto);
            }

            List<HomeworkQuestionDto> homeworkQueArr = new ArrayList();
            for (QuestionDto dto : questionArr) {
                if (!existHomeworkQuestionMap.containsKey(dto.getOid())) {
                    HomeworkQuestionDto questionDto = new HomeworkQuestionDto();
                    BeanUtils.copyProperties(bo, questionDto);
                    BeanUtils.copyProperties(dto, questionDto);
                    questionDto.setOid(IdUtil.simpleUUID());
                    questionDto.setQuestionOid(dto.getOid());
                    questionDto.setHomeworkOid(homeworkDto.getOid());
                    questionDto.setCreateBy(null);
                    questionDto.setUpdateBy(null);
                    questionDto.setCreateTime(null);
                    questionDto.setUpdateTime(null);
                    homeworkQueArr.add(questionDto);
                }
            }
            if (CollectionUtil.isNotEmpty(homeworkQueArr)) {
                homeworkQuestionService.saveBatch(homeworkQueArr);
            }
        }
        return homeworkDto;
    }

    public void saveQuestion(ExamPaper bean, String userId, String paperId) {
        ExamPaper.Head head = bean.getHead();
        XkwPaperInfoDto xkwPaperInfoDto = new XkwPaperInfoDto();
        xkwPaperInfoDto.setCourseId(bean.getCourse_id());
        xkwPaperInfoDto.setHeadTestInfo(head.getTest_info());
        xkwPaperInfoDto.setHeadMainTitle(head.getMain_title());
        xkwPaperInfoDto.setHeadNotice(head.getNotice());
        xkwPaperInfoDto.setHeadSubTitle(head.getSub_title());
        xkwPaperInfoDto.setHeadStudentInput(head.getStudent_input());
        xkwPaperInfoDto.setOid(IdUtil.simpleUUID());
        xkwPaperInfoDto.setCreateBy(userId);
        xkwPaperInfoDto.setCreateTime(new Date());
        xkwPaperInfoDto.setPaperId(paperId);
        xkwPaperInfoDto.setBodyQuestions(JSONUtil.toJsonStr(bean.getBody()));
        baseMapper.insert(xkwPaperInfoDto);
    }

    @Override
    public AjaxResult updateXkwPaperInfo(XkwPaperInfoBo xkwPaperInfoBo) {
        LambdaQueryWrapper<XkwPaperInfoDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(XkwPaperInfoDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(XkwPaperInfoDto::getOid, xkwPaperInfoBo.getOid());
        XkwPaperInfoDto xkwPaperInfo = getOne(lqw);
        Long id = xkwPaperInfo.getId();
        if (xkwPaperInfo == null) {
            return AjaxResult.fail("保存失败");
        }
        BeanUtils.copyProperties(xkwPaperInfoBo, xkwPaperInfo);
        xkwPaperInfo.setId(id);
        if (updateById(xkwPaperInfo)) {
            return AjaxResult.success("保存成功");
        } else {
            return AjaxResult.fail("保存失败");
        }
    }

    @Override
    public XkwPaperInfoVo getDetail(String oid) {
        LambdaQueryWrapper<XkwPaperInfoDto> lqw = new LambdaQueryWrapper<>();
        lqw.eq(XkwPaperInfoDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        lqw.eq(XkwPaperInfoDto::getOid, oid);
        XkwPaperInfoDto xkwPaperInfo = getOne(lqw);
        XkwPaperInfoVo xkwPaperInfoVo = new XkwPaperInfoVo();
        if (xkwPaperInfo != null) {
            BeanUtils.copyProperties(xkwPaperInfo, xkwPaperInfoVo);
        }
        return xkwPaperInfoVo;
    }

}