package com.light.aiszzy.practiceBook.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.light.aiszzy.practiceBook.event.PracticeBookCatalogAddEvent;
import com.light.aiszzy.practiceBook.event.PracticeBookCatalogDelEvent;
import com.light.utils.TreeUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.practiceBook.entity.dto.PracticeBookCatalogDto;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookCatalogConditionBo;
import com.light.aiszzy.practiceBook.entity.bo.PracticeBookCatalogBo;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookCatalogVo;
import com.light.aiszzy.practiceBook.service.IPracticeBookCatalogService;
import com.light.aiszzy.practiceBook.mapper.PracticeBookCatalogMapper;
import com.light.core.entity.AjaxResult;
import org.springframework.transaction.annotation.Transactional;

/**
 * 教辅目录表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Service
public class PracticeBookCatalogServiceImpl extends ServiceImpl<PracticeBookCatalogMapper, PracticeBookCatalogDto> implements IPracticeBookCatalogService {

	@Resource
	private PracticeBookCatalogMapper practiceBookCatalogMapper;

	@Resource
	private ApplicationEventPublisher applicationEventPublisher;
	
    @Override
	public List<PracticeBookCatalogVo> getPracticeBookCatalogListByCondition(PracticeBookCatalogConditionBo condition) {
        return practiceBookCatalogMapper.getPracticeBookCatalogListByCondition(condition);
	}

	@Override
	public AjaxResult addPracticeBookCatalog(PracticeBookCatalogBo practiceBookCatalogBo) {
		String parentOid = practiceBookCatalogBo.getParentOid();
		if(StrUtil.isEmpty(parentOid)) {
			parentOid = "0";
		}
		practiceBookCatalogBo.setLevel(1);
		// 父级 OID 不为空 && 不等于 0 ， 设置目录祖籍信息
		if(!parentOid.equals("0")){
			PracticeBookCatalogVo practiceBookCatalogVo = this.queryByOid(parentOid);
			if(practiceBookCatalogVo == null) {
				return AjaxResult.fail("父级目录不存在");
			}
			practiceBookCatalogBo.setSuperiorsOids(practiceBookCatalogVo.getSuperiorsOids().concat(",").concat(parentOid));
			practiceBookCatalogBo.setLevel(practiceBookCatalogVo.getLevel() + 1);
		}
		String practiceBookOid = practiceBookCatalogBo.getPracticeBookOid();
		// 获取最大排序
		Integer maxOrderNum = this.baseMapper.selectMaxOrderNumByParentOid(parentOid, practiceBookOid);

		PracticeBookCatalogDto practiceBookCatalog = new PracticeBookCatalogDto();
		BeanUtils.copyProperties(practiceBookCatalogBo, practiceBookCatalog);
		practiceBookCatalog.setIsDelete(StatusEnum.NOTDELETE.getCode());
		practiceBookCatalog.setOid(IdUtil.simpleUUID());
		practiceBookCatalog.setOrderNum(maxOrderNum + 1);
		boolean save = save(practiceBookCatalog);

		// 发送保存信息
		this.applicationEventPublisher.publishEvent(new PracticeBookCatalogAddEvent(this, practiceBookOid));
		return AjaxResult.success(practiceBookCatalog);
	}

	@Override
	public AjaxResult updatePracticeBookCatalog(PracticeBookCatalogBo practiceBookCatalogBo) {
		LambdaQueryWrapper<PracticeBookCatalogDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(PracticeBookCatalogDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(PracticeBookCatalogDto::getOid, practiceBookCatalogBo.getOid());
		PracticeBookCatalogDto practiceBookCatalog = getOne(lqw);
		Long id = practiceBookCatalog.getId();
		if(practiceBookCatalog == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(practiceBookCatalogBo, practiceBookCatalog);
		practiceBookCatalog.setId(id);
		if(updateById(practiceBookCatalog)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public PracticeBookCatalogVo getDetail(String oid) {
		LambdaQueryWrapper<PracticeBookCatalogDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(PracticeBookCatalogDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(PracticeBookCatalogDto::getOid, oid);
		PracticeBookCatalogDto practiceBookCatalog = getOne(lqw);
	    PracticeBookCatalogVo practiceBookCatalogVo = new PracticeBookCatalogVo();
		if(practiceBookCatalog != null){
			BeanUtils.copyProperties(practiceBookCatalog, practiceBookCatalogVo);
		}
		return practiceBookCatalogVo;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult<Void> delAndSaveBatchByPracticeBookOid(String practiceBookOid, List<PracticeBookCatalogBo> catalogBOList) {

		// 根据教辅 OID 删除目录
		this.deleteByPracticeBookOid(practiceBookOid);

		// 保存新的目录信息
		List<PracticeBookCatalogDto> catalogDtoList = catalogBOList.stream().map(x -> BeanUtil.toBean(x, PracticeBookCatalogDto.class))
				.peek(x-> x.setPracticeBookOid(practiceBookOid)).collect(Collectors.toList());

		this.saveBatch(catalogDtoList);

		this.applicationEventPublisher.publishEvent(new PracticeBookCatalogAddEvent(this, practiceBookOid));
		return AjaxResult.success();
	}


	/**
	 * 根据教辅 OID 删除数据
	 * @param practiceBookOid
	 * @return boolean
	 */
	public boolean deleteByPracticeBookOid(String practiceBookOid) {
		LambdaUpdateWrapper<PracticeBookCatalogDto> lqw = new LambdaUpdateWrapper<>();
		lqw.eq(PracticeBookCatalogDto::getPracticeBookOid, practiceBookOid);
		lqw.set(PracticeBookCatalogDto::getIsDelete, StatusEnum.ISDELETE.getCode());
		return this.update(lqw);
	}

	@Override
	public Integer queryCountByPracticeBookOid(String practiceBookOid) {
		QueryWrapper<PracticeBookCatalogDto> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(PracticeBookCatalogDto::getPracticeBookOid, practiceBookOid);
		queryWrapper.lambda().eq(PracticeBookCatalogDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		return this.baseMapper.selectCount(queryWrapper);
	}

	public PracticeBookCatalogVo queryByOid(String oid) {
		LambdaQueryWrapper<PracticeBookCatalogDto> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(PracticeBookCatalogDto::getOid, oid);
		queryWrapper.eq(PracticeBookCatalogDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		PracticeBookCatalogDto practiceBookCatalogDto = this.baseMapper.selectOne(queryWrapper);
		if(practiceBookCatalogDto == null){
			return null;
		}
		return BeanUtil.toBean(practiceBookCatalogDto, PracticeBookCatalogVo.class);
	}

	@Override
	public boolean deleteByOid(String oid) {

		PracticeBookCatalogVo practiceBookCatalogVo = this.queryByOid(oid);
		if(practiceBookCatalogVo == null){
			return true;
		}
		// 根据 OId 删除 包含子集
		int i = this.practiceBookCatalogMapper.deleteByOid(oid);

		// 发送通知
		this.applicationEventPublisher.publishEvent(new PracticeBookCatalogDelEvent(this, practiceBookCatalogVo.getPracticeBookOid(), oid));

		return i > 0;
	}




	@Override
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult<Void> resetOrderNum( List<String> catalogList) {
		List<PracticeBookCatalogVo> practiceBookCatalogVos = this.queryByOidList(catalogList);
		long bookOidCount = practiceBookCatalogVos.stream().map(PracticeBookCatalogVo::getPracticeBookOid).distinct().count();
		if(bookOidCount > 1) {
			return AjaxResult.fail("目录所属教辅不统一");
		}
		long catalogCount = practiceBookCatalogVos.stream().map(PracticeBookCatalogVo::getParentOid).distinct().count();
		if(catalogCount > 1) {
			return AjaxResult.fail("非同级别目录，不支持设置排序");
		}
		Map<String, PracticeBookCatalogVo> idCatalogMap = practiceBookCatalogVos.stream().collect(Collectors.toMap(PracticeBookCatalogVo::getOid, x -> x));
		AtomicInteger i = new AtomicInteger(1);
		List<PracticeBookCatalogDto> catalogDtoList = catalogList.stream().filter(idCatalogMap::containsKey).map(x -> {
			PracticeBookCatalogDto practiceBookCatalogDto = new PracticeBookCatalogDto();
			practiceBookCatalogDto.setId(idCatalogMap.get(x).getId());
			practiceBookCatalogDto.setOrderNum(i.getAndIncrement());
			return practiceBookCatalogDto;
		}).collect(Collectors.toList());

		this.updateBatchById(catalogDtoList);
		return AjaxResult.success();
	}

	@Override
	public List<PracticeBookCatalogVo> queryByOidList(List<String> catalogList) {
		return this.baseMapper.selectListByOidList(catalogList);
	}

	@Override
	public List<PracticeBookCatalogVo> queryTreeListByPracticeBookOid(String practiceBookOid) {
		List<PracticeBookCatalogVo> practiceBookCatalogVos = this.queryByPracticeBookOid(practiceBookOid);
		if(CollectionUtil.isEmpty(practiceBookCatalogVos)){
			return Collections.emptyList();
		}
		return TreeUtil.tree(practiceBookCatalogVos, "0", (x) -> {});
	}

	@Override
	public List<PracticeBookCatalogVo> queryByPracticeBookOid(String practiceBookOid) {
		return this.baseMapper.selectByPracticeBookOid(practiceBookOid);
	}
}