package com.light.aiszzy.homework.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.homework.entity.dto.HomeworkAllDoubtDto;
import com.light.aiszzy.homework.entity.bo.HomeworkAllDoubtConditionBo;
import com.light.aiszzy.homework.entity.vo.HomeworkAllDoubtVo;

/**
 * 作业疑问项表Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-08-02 15:28:43
 */
public interface HomeworkAllDoubtMapper extends BaseMapper<HomeworkAllDoubtDto> {

	List<HomeworkAllDoubtVo> getHomeworkAllDoubtListByCondition(HomeworkAllDoubtConditionBo condition);

}
