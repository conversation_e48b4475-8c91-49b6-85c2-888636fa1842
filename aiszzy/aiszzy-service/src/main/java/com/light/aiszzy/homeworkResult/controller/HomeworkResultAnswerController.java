package com.light.aiszzy.homeworkResult.controller;

import java.util.List;

import javax.validation.constraints.NotNull;

import com.light.aiszzy.homework.entity.bo.HomeworkBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkPageResultBo;
import com.light.aiszzy.utils.SqlUtil;
import io.swagger.annotations.Api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultAnswerConditionBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultAnswerBo;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultAnswerVo;
import com.light.aiszzy.homeworkResult.service.IHomeworkResultAnswerService;

import com.light.aiszzy.homeworkResult.api.HomeworkResultAnswerApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 学生题目答案表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@RestController
@Validated
@Api(value = "", tags = "学生题目答案表接口")
public class HomeworkResultAnswerController implements HomeworkResultAnswerApi {

    @Autowired
    private IHomeworkResultAnswerService homeworkResultAnswerService;

    public AjaxResult<PageInfo<HomeworkResultAnswerVo>> getHomeworkResultAnswerPageListByCondition(@RequestBody HomeworkResultAnswerConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());

        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), SqlUtil.sanitizeOrderBy(condition.getOrderBy(),condition.getClass()));
        PageInfo<HomeworkResultAnswerVo> pageInfo = new PageInfo<>(homeworkResultAnswerService.getHomeworkResultAnswerListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<HomeworkResultAnswerVo>> getHomeworkResultAnswerListByCondition(@RequestBody HomeworkResultAnswerConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(homeworkResultAnswerService.getHomeworkResultAnswerListByCondition(condition));
    }

    public AjaxResult addHomeworkResultAnswer(@Validated @RequestBody HomeworkResultAnswerBo homeworkResultAnswerBo) {
        return homeworkResultAnswerService.addHomeworkResultAnswer(homeworkResultAnswerBo);
    }

    public AjaxResult updateHomeworkResultAnswer(@Validated @RequestBody HomeworkResultAnswerBo homeworkResultAnswerBo) {
        if (null == homeworkResultAnswerBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return homeworkResultAnswerService.updateHomeworkResultAnswer(homeworkResultAnswerBo);
    }

    public AjaxResult<HomeworkResultAnswerVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(homeworkResultAnswerService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            HomeworkResultAnswerBo homeworkResultAnswerBo = new HomeworkResultAnswerBo();
            homeworkResultAnswerBo.setOid(oid);
            homeworkResultAnswerBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return homeworkResultAnswerService.updateHomeworkResultAnswer(homeworkResultAnswerBo);
    }
}
