package com.light.aiszzy.statistics.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.light.aiszzy.statistics.entity.bo.HomeworkClassQuestionStatisticsBo;
import com.light.aiszzy.statistics.entity.bo.HomeworkClassQuestionStatisticsConditionBo;
import com.light.aiszzy.statistics.entity.dto.HomeworkClassQuestionStatisticsDto;
import com.light.aiszzy.statistics.entity.vo.HomeworkClassQuestionStatisticsVo;
import com.light.aiszzy.statistics.mapper.HomeworkClassQuestionStatisticsMapper;
import com.light.aiszzy.statistics.service.IHomeworkClassQuestionStatisticsService;
import com.light.core.constants.SystemConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.StringUtils;
import com.light.enums.SzzyRedisKeyEnum;
import com.light.redis.component.RedisComponent;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
/**
 * 作业班级题目正确率接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 10:39:09
 */
@Service
public class HomeworkClassQuestionStatisticsServiceImpl extends ServiceImpl<HomeworkClassQuestionStatisticsMapper, HomeworkClassQuestionStatisticsDto> implements IHomeworkClassQuestionStatisticsService {

	@Resource
	private HomeworkClassQuestionStatisticsMapper homeworkClassQuestionStatisticsMapper;

	@Resource
	private RedisComponent redisComponent;
	
    @Override
	public List<HomeworkClassQuestionStatisticsVo> getHomeworkClassQuestionStatisticsListByCondition(HomeworkClassQuestionStatisticsConditionBo condition) {
        return homeworkClassQuestionStatisticsMapper.getHomeworkClassQuestionStatisticsListByCondition(condition);
	}

	@Override
	public AjaxResult addHomeworkClassQuestionStatistics(HomeworkClassQuestionStatisticsBo homeworkClassQuestionStatisticsBo) {
		HomeworkClassQuestionStatisticsDto homeworkClassQuestionStatistics = new HomeworkClassQuestionStatisticsDto();
		BeanUtils.copyProperties(homeworkClassQuestionStatisticsBo, homeworkClassQuestionStatistics);
		homeworkClassQuestionStatistics.setIsDelete(StatusEnum.NOTDELETE.getCode());
		homeworkClassQuestionStatistics.setOid(IdUtil.simpleUUID());
		if(save(homeworkClassQuestionStatistics)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateHomeworkClassQuestionStatistics(HomeworkClassQuestionStatisticsBo homeworkClassQuestionStatisticsBo) {
		LambdaQueryWrapper<HomeworkClassQuestionStatisticsDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkClassQuestionStatisticsDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkClassQuestionStatisticsDto::getOid, homeworkClassQuestionStatisticsBo.getOid());
		HomeworkClassQuestionStatisticsDto homeworkClassQuestionStatistics = getOne(lqw);
		Long id = homeworkClassQuestionStatistics.getId();
		if(homeworkClassQuestionStatistics == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(homeworkClassQuestionStatisticsBo, homeworkClassQuestionStatistics);
		homeworkClassQuestionStatistics.setId(id);
		if(updateById(homeworkClassQuestionStatistics)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public HomeworkClassQuestionStatisticsVo getDetail(String oid) {
		LambdaQueryWrapper<HomeworkClassQuestionStatisticsDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkClassQuestionStatisticsDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkClassQuestionStatisticsDto::getOid, oid);
		HomeworkClassQuestionStatisticsDto homeworkClassQuestionStatistics = getOne(lqw);
	    HomeworkClassQuestionStatisticsVo homeworkClassQuestionStatisticsVo = new HomeworkClassQuestionStatisticsVo();
		if(homeworkClassQuestionStatistics != null){
			BeanUtils.copyProperties(homeworkClassQuestionStatistics, homeworkClassQuestionStatisticsVo);
		}
		return homeworkClassQuestionStatisticsVo;
	}

	@Override
	public HomeworkClassQuestionStatisticsVo getDetailByHomeworkClassQuestion(String homeworkOid, Long classId, String questionOid) {

		//优先从Redis中取值
		String  redisKey = SzzyRedisKeyEnum.HOMEWORK_CLASS_QUESTION_STATISTICS.getValue() + homeworkOid + SystemConstants.SEPERATOR_COLON + classId + SystemConstants.SEPERATOR_COLON + questionOid + SystemConstants.SEPERATOR_COLON;
		Object object = redisComponent.get(redisKey);
		if (null != object) {
			HomeworkClassQuestionStatisticsVo homeworkClassQuestionStatisticsVo = JSON.parseObject(JSONUtil.toJsonStr(object), HomeworkClassQuestionStatisticsVo.class);

			if (homeworkClassQuestionStatisticsVo != null && StringUtils.isNotBlank(homeworkClassQuestionStatisticsVo.getHomeworkOid())) {
				return homeworkClassQuestionStatisticsVo;
			}
		}

		//缓存中没有，从数据库里查询
		LambdaQueryWrapper<HomeworkClassQuestionStatisticsDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkClassQuestionStatisticsDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkClassQuestionStatisticsDto::getHomeworkOid, homeworkOid);
		lqw.eq(HomeworkClassQuestionStatisticsDto::getClassId, classId);
		lqw.eq(HomeworkClassQuestionStatisticsDto::getQuestionOid, questionOid);
		HomeworkClassQuestionStatisticsDto homeworkClassQuestionStatistics = getOne(lqw);
		HomeworkClassQuestionStatisticsVo homeworkClassQuestionStatisticsVo = new HomeworkClassQuestionStatisticsVo();
		if(homeworkClassQuestionStatistics != null && StringUtils.isNotBlank(homeworkClassQuestionStatistics.getHomeworkOid())) {
			BeanUtils.copyProperties(homeworkClassQuestionStatistics, homeworkClassQuestionStatisticsVo);
			//同步入缓存
			redisComponent.set(redisKey, JSON.toJSONString(homeworkClassQuestionStatisticsVo),3600 * 24);
		}
		return homeworkClassQuestionStatisticsVo;
	}

	@Override
	public List<HomeworkClassQuestionStatisticsVo> queryByHomeworkClass(String homeworkOid, Long classId) {
		//优先从Redis中取值
		String  redisKey = SzzyRedisKeyEnum.HOMEWORK_CLASS_QUESTION_STATISTICS.getValue() + homeworkOid + SystemConstants.SEPERATOR_COLON + classId;
		Map<Object, Object> map = redisComponent.hmget(redisKey);
		if (null != map && !map.isEmpty()) {
			return map.values().stream().map(x-> JSON.parseObject(x.toString(), HomeworkClassQuestionStatisticsVo.class)).collect(Collectors.toList());
		}

		//缓存中没有，从数据库里查询
		LambdaQueryWrapper<HomeworkClassQuestionStatisticsDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkClassQuestionStatisticsDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkClassQuestionStatisticsDto::getHomeworkOid, homeworkOid);
		lqw.eq(HomeworkClassQuestionStatisticsDto::getClassId, classId);
		List<HomeworkClassQuestionStatisticsDto> list = this.baseMapper.selectList(lqw);
		if(CollUtil.isNotEmpty(list)) {
			Map<String, String> statisticsMap = list.stream().collect(Collectors.toMap(HomeworkClassQuestionStatisticsDto::getOid, JSON::toJSONString));
			//同步入缓存
			redisComponent.set(redisKey,statisticsMap ,3600 * 24);

			return list.stream().map(x-> BeanUtil.toBean(x, HomeworkClassQuestionStatisticsVo.class)).collect(Collectors.toList());
		}
		return Collections.emptyList();
	}
}