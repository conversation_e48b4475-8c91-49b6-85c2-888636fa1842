package com.light.aiszzy.statistics.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;

import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.statistics.entity.bo.QuestionStatisticsConditionBo;
import com.light.aiszzy.statistics.entity.bo.QuestionStatisticsBo;
import com.light.aiszzy.statistics.entity.vo.QuestionStatisticsVo;
import com.light.aiszzy.statistics.service.IQuestionStatisticsService;

import com.light.aiszzy.statistics.api.QuestionStatisticsApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import com.light.core.constants.SystemConstants;

/**
 * 题目正确率
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-08-02 18:11:10
 */
@RestController
@Validated
@Api(value = "", tags = "题目正确率接口")
public class QuestionStatisticsController implements QuestionStatisticsApi {

    @Autowired
    private IQuestionStatisticsService questionStatisticsService;

    public AjaxResult<PageInfo<QuestionStatisticsVo>> getQuestionStatisticsPageListByCondition(@RequestBody QuestionStatisticsConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<QuestionStatisticsVo> pageInfo = new PageInfo<>(questionStatisticsService.getQuestionStatisticsListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<QuestionStatisticsVo>> getQuestionStatisticsListByCondition(@RequestBody QuestionStatisticsConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(questionStatisticsService.getQuestionStatisticsListByCondition(condition));
    }

    public AjaxResult addQuestionStatistics(@Validated @RequestBody QuestionStatisticsBo questionStatisticsBo) {
        return questionStatisticsService.addQuestionStatistics(questionStatisticsBo);
    }

    public AjaxResult updateQuestionStatistics(@Validated @RequestBody QuestionStatisticsBo questionStatisticsBo) {
        if (null == questionStatisticsBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return questionStatisticsService.updateQuestionStatistics(questionStatisticsBo);
    }

    public AjaxResult<QuestionStatisticsVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(questionStatisticsService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            QuestionStatisticsBo questionStatisticsBo = new QuestionStatisticsBo();
            questionStatisticsBo.setOid(oid);
            questionStatisticsBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return questionStatisticsService.updateQuestionStatistics(questionStatisticsBo);
    }
}
