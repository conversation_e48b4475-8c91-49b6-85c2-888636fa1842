package com.light.aiszzy.statistics.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.statistics.entity.dto.QuestionStatisticsDto;
import com.light.aiszzy.statistics.entity.bo.QuestionStatisticsConditionBo;
import com.light.aiszzy.statistics.entity.bo.QuestionStatisticsBo;
import com.light.aiszzy.statistics.entity.vo.QuestionStatisticsVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 题目正确率接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-08-02 18:11:10
 */
public interface IQuestionStatisticsService extends IService<QuestionStatisticsDto> {

    List<QuestionStatisticsVo> getQuestionStatisticsListByCondition(QuestionStatisticsConditionBo condition);

	AjaxResult addQuestionStatistics(QuestionStatisticsBo questionStatisticsBo);

	AjaxResult updateQuestionStatistics(QuestionStatisticsBo questionStatisticsBo);

    QuestionStatisticsVo getDetail(String oid);

    QuestionStatisticsVo getDetailByQuestionOid(String questionOid);

}

