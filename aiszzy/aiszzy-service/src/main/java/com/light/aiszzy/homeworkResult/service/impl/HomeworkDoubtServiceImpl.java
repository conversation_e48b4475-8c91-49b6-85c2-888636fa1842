package com.light.aiszzy.homeworkResult.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.light.aiszzy.homeworkResult.entity.dto.HomeworkPageResultDto;
import com.light.aiszzy.homeworkResult.entity.dto.HomeworkResultAnswerDto;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkDoubtStatVo;
import com.light.aiszzy.homeworkResult.mapper.HomeworkPageResultMapper;
import com.light.aiszzy.homeworkResult.mapper.HomeworkResultAnswerMapper;
import com.light.aiszzy.homeworkResult.service.HomeworkDoubtService;
import com.light.contants.AISzzyConstants;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 作业疑问项接口实现类
 */
@Service
public class HomeworkDoubtServiceImpl implements HomeworkDoubtService {

    @Resource
    private HomeworkPageResultMapper homeworkPageResultMapper;

    @Resource
    private HomeworkResultAnswerMapper homeworkResultAnswerMapper;

    @Override
    public HomeworkDoubtStatVo statDoubt(String homeworkOid) {
        if (StringUtils.isEmpty(homeworkOid)) {
            return null;
        }

        LambdaQueryWrapper<HomeworkPageResultDto> noStudentLqw = new LambdaQueryWrapper<>();
        noStudentLqw.eq(HomeworkPageResultDto::getHomeworkOid, homeworkOid);
        noStudentLqw.eq(HomeworkPageResultDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        noStudentLqw.eq(HomeworkPageResultDto::getDoubtType, AISzzyConstants.DoubtType.NOERROR.getType());
        // 学生未匹配数量
        Integer noStudentNum = homeworkPageResultMapper.selectCount(noStudentLqw);

        // 学生重复数量
        LambdaQueryWrapper<HomeworkPageResultDto> repeatLqw = new LambdaQueryWrapper<>();
        repeatLqw.eq(HomeworkPageResultDto::getHomeworkOid, homeworkOid);
        repeatLqw.eq(HomeworkPageResultDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        repeatLqw.eq(HomeworkPageResultDto::getDoubtType, AISzzyConstants.DoubtType.REPEAT.getType());
        Integer repeatNum = homeworkPageResultMapper.selectCount(repeatLqw);

        // 学生疑问数量
        LambdaQueryWrapper<HomeworkResultAnswerDto> answerLqw = new LambdaQueryWrapper<>();
        answerLqw.eq(HomeworkResultAnswerDto::getHomeworkOid, homeworkOid);
        answerLqw.eq(HomeworkResultAnswerDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
        answerLqw.eq(HomeworkResultAnswerDto::getIsDoubt, AISzzyConstants.DoubtStatus.EXIST.getType());
        Integer unknownQuestionNum = homeworkResultAnswerMapper.selectCount(answerLqw);

        return new HomeworkDoubtStatVo(homeworkOid, noStudentNum, repeatNum, unknownQuestionNum);
    }
}
