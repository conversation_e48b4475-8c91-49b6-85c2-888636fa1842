package com.light.aiszzy.homeworkResult.controller;

import com.light.aiszzy.homeworkResult.api.HomeworkDoubtApi;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkDoubtStatVo;
import com.light.aiszzy.homeworkResult.service.HomeworkDoubtService;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

@RestController
@Validated
@Api(value = "", tags = "作业疑问项接口")
public class HomeworkDoubtController implements HomeworkDoubtApi {

    @Resource
    private HomeworkDoubtService homeworkDoubtService;

    @Override
    public AjaxResult<HomeworkDoubtStatVo> stat(@NotNull(message = "请选择作业") String homeworkOid) {

        HomeworkDoubtStatVo statVo = homeworkDoubtService.statDoubt(homeworkOid);
        if (statVo != null) {
            return AjaxResult.success(statVo);
        } else {
            return AjaxResult.fail();
        }
    }
}
