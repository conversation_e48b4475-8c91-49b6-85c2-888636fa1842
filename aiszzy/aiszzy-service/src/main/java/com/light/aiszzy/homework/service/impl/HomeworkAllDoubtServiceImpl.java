package com.light.aiszzy.homework.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.light.core.constants.SystemConstants;
import com.light.core.utils.StringUtils;
import com.light.enums.SzzyRedisKeyEnum;
import com.light.redis.component.RedisComponent;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.homework.entity.dto.HomeworkAllDoubtDto;
import com.light.aiszzy.homework.entity.bo.HomeworkAllDoubtConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkAllDoubtBo;
import com.light.aiszzy.homework.entity.vo.HomeworkAllDoubtVo;
import com.light.aiszzy.homework.service.IHomeworkAllDoubtService;
import com.light.aiszzy.homework.mapper.HomeworkAllDoubtMapper;
import com.light.core.entity.AjaxResult;
/**
 * 作业疑问项表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-08-02 15:28:43
 */
@Service
public class HomeworkAllDoubtServiceImpl extends ServiceImpl<HomeworkAllDoubtMapper, HomeworkAllDoubtDto> implements IHomeworkAllDoubtService {

	@Resource
	private HomeworkAllDoubtMapper homeworkAllDoubtMapper;


	@Resource
	private RedisComponent redisComponent;
	
    @Override
	public List<HomeworkAllDoubtVo> getHomeworkAllDoubtListByCondition(HomeworkAllDoubtConditionBo condition) {
        return homeworkAllDoubtMapper.getHomeworkAllDoubtListByCondition(condition);
	}

	@Override
	public AjaxResult addHomeworkAllDoubt(HomeworkAllDoubtBo homeworkAllDoubtBo) {
		HomeworkAllDoubtDto homeworkAllDoubt = new HomeworkAllDoubtDto();
		BeanUtils.copyProperties(homeworkAllDoubtBo, homeworkAllDoubt);
		homeworkAllDoubt.setOid(IdUtil.simpleUUID());
		if(save(homeworkAllDoubt)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateHomeworkAllDoubt(HomeworkAllDoubtBo homeworkAllDoubtBo) {
		LambdaQueryWrapper<HomeworkAllDoubtDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkAllDoubtDto::getOid, homeworkAllDoubtBo.getOid());
		HomeworkAllDoubtDto homeworkAllDoubt = getOne(lqw);
		Long id = homeworkAllDoubt.getId();
		if(homeworkAllDoubt == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(homeworkAllDoubtBo, homeworkAllDoubt);
		homeworkAllDoubt.setId(id);
		if(updateById(homeworkAllDoubt)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public HomeworkAllDoubtVo getDetail(String oid) {
		LambdaQueryWrapper<HomeworkAllDoubtDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkAllDoubtDto::getOid, oid);
		HomeworkAllDoubtDto homeworkAllDoubt = getOne(lqw);
	    HomeworkAllDoubtVo homeworkAllDoubtVo = new HomeworkAllDoubtVo();
		if(homeworkAllDoubt != null){
			BeanUtils.copyProperties(homeworkAllDoubt, homeworkAllDoubtVo);
		}
		return homeworkAllDoubtVo;
	}

	@Override
	public HomeworkAllDoubtVo getDetailByHomeworkOid(String homeworkOid) {

		//优先从Redis中取值
		String  redisKey = SzzyRedisKeyEnum.HOMEWORK_All_DOUBT.getValue() + homeworkOid + SystemConstants.SEPERATOR_COLON;
		Object object = redisComponent.get(redisKey);
		if (null != object) {
			HomeworkAllDoubtVo homeworkAllDoubtVo = JSON.parseObject(JSONUtil.toJsonStr(object), HomeworkAllDoubtVo.class);

			if (homeworkAllDoubtVo != null && StringUtils.isNotBlank(homeworkAllDoubtVo.getOrgCode())) {
				return homeworkAllDoubtVo;
			}
		}

		LambdaQueryWrapper<HomeworkAllDoubtDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkAllDoubtDto::getHomeworkOid, homeworkOid);
		HomeworkAllDoubtDto homeworkAllDoubt = getOne(lqw);
		HomeworkAllDoubtVo homeworkAllDoubtVo = new HomeworkAllDoubtVo();
		if(homeworkAllDoubt != null){
			BeanUtils.copyProperties(homeworkAllDoubt, homeworkAllDoubtVo);
			//同步入缓存
			redisComponent.set(redisKey, JSON.toJSONString(homeworkAllDoubtVo),3600 * 24);
		}
		return homeworkAllDoubtVo;
	}

}