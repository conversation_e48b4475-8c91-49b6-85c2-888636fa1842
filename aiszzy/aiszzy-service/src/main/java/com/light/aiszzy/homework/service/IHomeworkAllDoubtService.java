package com.light.aiszzy.homework.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.homework.entity.dto.HomeworkAllDoubtDto;
import com.light.aiszzy.homework.entity.bo.HomeworkAllDoubtConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkAllDoubtBo;
import com.light.aiszzy.homework.entity.vo.HomeworkAllDoubtVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 作业疑问项表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-08-02 15:28:43
 */
public interface IHomeworkAllDoubtService extends IService<HomeworkAllDoubtDto> {

    List<HomeworkAllDoubtVo> getHomeworkAllDoubtListByCondition(HomeworkAllDoubtConditionBo condition);

	AjaxResult addHomeworkAllDoubt(HomeworkAllDoubtBo homeworkAllDoubtBo);

	AjaxResult updateHomeworkAllDoubt(HomeworkAllDoubtBo homeworkAllDoubtBo);

    HomeworkAllDoubtVo getDetail(String oid);

    HomeworkAllDoubtVo getDetailByHomeworkOid(String homeworkOid);

}

