package com.light.aiszzy.homework.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.List;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.Api;

import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.homework.entity.bo.HomeworkAllDoubtConditionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkAllDoubtBo;
import com.light.aiszzy.homework.entity.vo.HomeworkAllDoubtVo;
import com.light.aiszzy.homework.service.IHomeworkAllDoubtService;

import com.light.aiszzy.homework.api.HomeworkAllDoubtApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

import com.light.core.constants.SystemConstants;

/**
 * 作业疑问项表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-08-02 15:28:43
 */
@RestController
@Validated
@Api(value = "", tags = "作业疑问项表接口")
public class HomeworkAllDoubtController implements HomeworkAllDoubtApi {

    @Autowired
    private IHomeworkAllDoubtService homeworkAllDoubtService;

    public AjaxResult<PageInfo<HomeworkAllDoubtVo>> getHomeworkAllDoubtPageListByCondition(@RequestBody HomeworkAllDoubtConditionBo condition) {
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), condition.getOrderBy());
        PageInfo<HomeworkAllDoubtVo> pageInfo = new PageInfo<>(homeworkAllDoubtService.getHomeworkAllDoubtListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<HomeworkAllDoubtVo>> getHomeworkAllDoubtListByCondition(@RequestBody HomeworkAllDoubtConditionBo condition) {
        return AjaxResult.success(homeworkAllDoubtService.getHomeworkAllDoubtListByCondition(condition));
    }

    public AjaxResult addHomeworkAllDoubt(@Validated @RequestBody HomeworkAllDoubtBo homeworkAllDoubtBo) {
        return homeworkAllDoubtService.addHomeworkAllDoubt(homeworkAllDoubtBo);
    }

    public AjaxResult updateHomeworkAllDoubt(@Validated @RequestBody HomeworkAllDoubtBo homeworkAllDoubtBo) {
        if (null == homeworkAllDoubtBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return homeworkAllDoubtService.updateHomeworkAllDoubt(homeworkAllDoubtBo);
    }

    public AjaxResult<HomeworkAllDoubtVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(homeworkAllDoubtService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            HomeworkAllDoubtBo homeworkAllDoubtBo = new HomeworkAllDoubtBo();
            homeworkAllDoubtBo.setOid(oid);
        return homeworkAllDoubtService.updateHomeworkAllDoubt(homeworkAllDoubtBo);
    }
}
