package com.light.aiszzy.resultUploadFile.bo;

import com.light.aiszzy.homework.entity.dto.HomeworkDto;
import com.light.aiszzy.homework.entity.dto.HomeworkPageDto;
import com.light.aiszzy.homework.entity.dto.HomeworkQuestionDto;
import com.light.aiszzy.homeworkResult.entity.dto.HomeworkPageResultDto;
import com.light.aiszzy.homeworkResult.entity.dto.HomeworkResultAnswerDto;
import com.light.aiszzy.homeworkResult.entity.dto.HomeworkResultDto;
import com.light.aiszzy.resultUploadFile.entity.vo.ResultUploadFileVo;
import com.light.user.student.entity.vo.StudentVo;
import lombok.Data;

import java.awt.image.BufferedImage;
import java.util.List;
import java.util.Map;

/**
 * 作业上传结果图片处理上下文
 */
@Data
public class ResultUploadFileDealContext {

    /**
     * 是否处理失败
     */
    private boolean dealFailed = false;
    /**
     * 处理情况描述
     */
    private String dealDetail;
    /**
     * 处理结果信息
     */
    private String dealResult;

    /**
     * 学生信息
     */
    private StudentVo studentInfo;

    /**
     * ocr学生号
     */
    private String ocrStuNo;

    /**
     * ocr学生姓名
     */
    private String ocrStuName;
    /**
     * 原始上传图片信息
     */
    private ResultUploadFileVo resultUploadFileVo;

    /**
     * 作业信息
     */
    private HomeworkDto homeworkDto;

    /**
     * 是否二维码在第一页
     */
    private Boolean qrCodeFirstFlag;

    /**
     * 二维码信息
     * 类型#作业编号#页码
     * eg：1#0oxl3rg4#1
     */
    private String qrDeCode;
    /**
     * 二维码中类型
     */
    private Integer type;
    /**
     * 二维码中作业编号
     */
    private String homeworkCode;

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 原始图片1信息
     */
    private BufferedImage srcBufferedImageOne;
    /**
     * 原始图片2信息
     */
    private BufferedImage srcBufferedImageTwo;
    /**
     * 旋转后图片1信息，该图片为包含二维码的第一页，且正向放置
     */
    private BufferedImage destBufferedImageOne;
    /**
     * 旋转后图片1的，文件服务器地址，有值说明已上传
     */
    private String destBufferedImageOneUrl;
    /**
     * 旋转后图片1信息，仅保留红色像素
     */
    private BufferedImage destBufferedImageOneOnlyRed;
    /**
     * 旋转后图片1信息，仅保留红色像素，文件服务器地址，有值说明已上传
     */
    private String destBufferedImageOneOnlyRedUrl;
    /**
     * 旋转后图片2信息，该图片为不包含二维码的第二页，且正向放置
     */
    private BufferedImage destBufferedImageTwo;
    /**
     * 旋转后图片2的，文件服务器地址，有值说明已上传
     */
    private String destBufferedImageTwoUrl;
    /**
     * 旋转后图片2信息，仅保留红色像素
     */
    private BufferedImage destBufferedImageTwoOnlyRed;
    /**
     * 旋转后图片2信息，仅保留红色像素，文件服务器地址，有值说明已上传
     */
    private String destBufferedImageTwoOnlyRedUrl;
    /**
     * 正面的作业页信息
     */
    private HomeworkPageDto homeworkPageDtoOne;
    /**
     * 第一页作业题目数量
     */
    private int homeworkPageOneQuestionNum;
    /**
     * 反面的作业页信息，如果为空则无反面作业页信息
     */
    private HomeworkPageDto homeworkPageDtoTwo;
    /**
     * 第二页作业题目数量
     */
    private int homeworkPageTwoQuestionNum;
    /**
     * homeworkQuestionDtoMap 作业题信息
     */
    private Map<String, HomeworkQuestionDto> homeworkQuestionDtoMap;

    /**
     * 第一页作业结果答案信息
     */
    private List<HomeworkResultAnswerDto> pageOneHomeworkResultAnswerDtoList;
    /**
     * 第二页作业结果答案信息
     */
    private List<HomeworkResultAnswerDto> pageTwoHomeworkResultAnswerDtoList;

    /**
     * 作业结果信息
     */
    private HomeworkResultDto homeworkResultDto;

    /**
     * 正面作业结果答案信息
     */
    private HomeworkPageResultDto homeworkPageResultDto;

    /**
     * 正反面正确题目数
     */
    private int rightNum;

    /**
     * 正反面错误题目数
     */
    private int wrongNum;

    /**
     * 正反面未知题目数
     */
    private int unknownNum;

}
