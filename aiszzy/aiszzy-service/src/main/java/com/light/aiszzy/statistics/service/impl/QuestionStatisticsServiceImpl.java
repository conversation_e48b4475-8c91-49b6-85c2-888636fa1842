package com.light.aiszzy.statistics.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.light.core.constants.SystemConstants;
import com.light.core.utils.StringUtils;
import com.light.enums.SzzyRedisKeyEnum;
import com.light.redis.component.RedisComponent;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.statistics.entity.dto.QuestionStatisticsDto;
import com.light.aiszzy.statistics.entity.bo.QuestionStatisticsConditionBo;
import com.light.aiszzy.statistics.entity.bo.QuestionStatisticsBo;
import com.light.aiszzy.statistics.entity.vo.QuestionStatisticsVo;
import com.light.aiszzy.statistics.service.IQuestionStatisticsService;
import com.light.aiszzy.statistics.mapper.QuestionStatisticsMapper;
import com.light.core.entity.AjaxResult;
/**
 * 题目正确率接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-08-02 18:11:10
 */
@Service
public class QuestionStatisticsServiceImpl extends ServiceImpl<QuestionStatisticsMapper, QuestionStatisticsDto> implements IQuestionStatisticsService {

	@Resource
	private QuestionStatisticsMapper questionStatisticsMapper;

	@Resource
	private RedisComponent redisComponent;
	
    @Override
	public List<QuestionStatisticsVo> getQuestionStatisticsListByCondition(QuestionStatisticsConditionBo condition) {
        return questionStatisticsMapper.getQuestionStatisticsListByCondition(condition);
	}

	@Override
	public AjaxResult addQuestionStatistics(QuestionStatisticsBo questionStatisticsBo) {
		QuestionStatisticsDto questionStatistics = new QuestionStatisticsDto();
		BeanUtils.copyProperties(questionStatisticsBo, questionStatistics);
		questionStatistics.setIsDelete(StatusEnum.NOTDELETE.getCode());
		questionStatistics.setOid(IdUtil.simpleUUID());
		if(save(questionStatistics)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateQuestionStatistics(QuestionStatisticsBo questionStatisticsBo) {
		LambdaQueryWrapper<QuestionStatisticsDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(QuestionStatisticsDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(QuestionStatisticsDto::getOid, questionStatisticsBo.getOid());
		QuestionStatisticsDto questionStatistics = getOne(lqw);
		Long id = questionStatistics.getId();
		if(questionStatistics == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(questionStatisticsBo, questionStatistics);
		questionStatistics.setId(id);
		if(updateById(questionStatistics)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public QuestionStatisticsVo getDetail(String oid) {
		LambdaQueryWrapper<QuestionStatisticsDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(QuestionStatisticsDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(QuestionStatisticsDto::getOid, oid);
		QuestionStatisticsDto questionStatistics = getOne(lqw);
	    QuestionStatisticsVo questionStatisticsVo = new QuestionStatisticsVo();
		if(questionStatistics != null){
			BeanUtils.copyProperties(questionStatistics, questionStatisticsVo);
		}
		return questionStatisticsVo;
	}

	@Override
	public QuestionStatisticsVo getDetailByQuestionOid(String questionOid) {

		//优先从Redis中取值
		String  redisKey = SzzyRedisKeyEnum.QUESTION_STATISTICS.getValue() + questionOid + SystemConstants.SEPERATOR_COLON;
		Object object = redisComponent.get(redisKey);
		if (null != object) {
			QuestionStatisticsVo questionStatisticsVo = JSON.parseObject(JSONUtil.toJsonStr(object), QuestionStatisticsVo.class);

			if (questionStatisticsVo != null && StringUtils.isNotBlank(questionStatisticsVo.getQuestionOid())) {
				return questionStatisticsVo;
			}
		}

		LambdaQueryWrapper<QuestionStatisticsDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(QuestionStatisticsDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(QuestionStatisticsDto::getQuestionOid, questionOid);
		QuestionStatisticsDto questionStatistics = getOne(lqw);
		QuestionStatisticsVo questionStatisticsVo = new QuestionStatisticsVo();
		if(questionStatistics != null){
			BeanUtils.copyProperties(questionStatistics, questionStatisticsVo);

			//同步入缓存
			redisComponent.set(redisKey, JSON.toJSONString(questionStatisticsVo),3600 * 24);
		}
		return questionStatisticsVo;
	}

}