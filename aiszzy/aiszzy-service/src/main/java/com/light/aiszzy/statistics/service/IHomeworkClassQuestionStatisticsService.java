package com.light.aiszzy.statistics.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.statistics.entity.dto.HomeworkClassQuestionStatisticsDto;
import com.light.aiszzy.statistics.entity.bo.HomeworkClassQuestionStatisticsConditionBo;
import com.light.aiszzy.statistics.entity.bo.HomeworkClassQuestionStatisticsBo;
import com.light.aiszzy.statistics.entity.vo.HomeworkClassQuestionStatisticsVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 作业班级题目正确率接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 10:39:09
 */
public interface IHomeworkClassQuestionStatisticsService extends IService<HomeworkClassQuestionStatisticsDto> {

    List<HomeworkClassQuestionStatisticsVo> getHomeworkClassQuestionStatisticsListByCondition(HomeworkClassQuestionStatisticsConditionBo condition);

	AjaxResult addHomeworkClassQuestionStatistics(HomeworkClassQuestionStatisticsBo homeworkClassQuestionStatisticsBo);

	AjaxResult updateHomeworkClassQuestionStatistics(HomeworkClassQuestionStatisticsBo homeworkClassQuestionStatisticsBo);

    HomeworkClassQuestionStatisticsVo getDetail(String oid);

    HomeworkClassQuestionStatisticsVo getDetailByHomeworkClassQuestion(String homeworkOid, Long classId, String questionOid);


    List<HomeworkClassQuestionStatisticsVo> queryByHomeworkClass(String homeworkOid, Long classId);

}

