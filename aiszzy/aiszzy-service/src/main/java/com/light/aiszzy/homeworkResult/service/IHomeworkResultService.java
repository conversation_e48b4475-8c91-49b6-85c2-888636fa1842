package com.light.aiszzy.homeworkResult.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.homeworkResult.entity.dto.HomeworkResultDto;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultConditionBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultBo;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultVo;
import com.light.core.entity.AjaxResult;

import java.util.List;
import java.util.Map;

/**
 * 校本作业结果表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
public interface IHomeworkResultService extends IService<HomeworkResultDto> {

    List<HomeworkResultVo> getHomeworkResultListByCondition(HomeworkResultConditionBo condition);

    List<HomeworkResultVo> getDuplicateHomeworkResultListByCondition(HomeworkResultConditionBo condition);

	AjaxResult addHomeworkResult(HomeworkResultBo homeworkResultBo);

	AjaxResult updateHomeworkResult(HomeworkResultBo homeworkResultBo);

    HomeworkResultVo getDetail(String oid);

    HomeworkResultVo getDetailByHomeworkUserOid(String homeworkOid, String stuOid);

    /**
     *  根据作业 OID 班级 ID 查询 数据
     * @param homeworkOid 作业 OID
     * @param classId 班级 DI
     * @return {@link List }<{@link HomeworkResultVo }>
     */
    List<HomeworkResultVo> queryByHomeworkClassId(String homeworkOid,  Long classId);
}

