package com.light.aiszzy.homeworkResult.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.homeworkResult.entity.dto.HomeworkPageResultDto;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkPageResultConditionBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkPageResultBo;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkPageResultVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 扫描结构每页处理结果
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 15:52:36
 */
public interface IHomeworkPageResultService extends IService<HomeworkPageResultDto> {

    List<HomeworkPageResultVo> getHomeworkPageResultListByCondition(HomeworkPageResultConditionBo condition);

    AjaxResult addHomeworkPageResult(HomeworkPageResultBo homeworkPageResultBo);

    AjaxResult updateHomeworkPageResult(HomeworkPageResultBo homeworkPageResultBo);

    HomeworkPageResultVo getDetail(String oid);

    AjaxResult dealNoStudent(HomeworkPageResultBo bo);

    AjaxResult dealRepeat(HomeworkPageResultBo bo);

    /**
     * 根据作业oid，更新重复疑问项状态
     * 如果一份作业，存在结果页同页，同学生，则更新为存在重复的疑问项
     * 如果一份作业，原有结果页为重复，当前已不存在重复学生和页码，则更新为非疑问项
     * @param homeworkOid 作业id
     * @return 受影响的行数
     */
    int updateRepeatDoubtTypeByHomeworkOid(String homeworkOid);

}

