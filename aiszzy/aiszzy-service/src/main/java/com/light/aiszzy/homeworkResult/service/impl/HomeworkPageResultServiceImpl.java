package com.light.aiszzy.homeworkResult.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkPageResultBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkPageResultConditionBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultAnswerConditionBo;
import com.light.aiszzy.homeworkResult.entity.dto.HomeworkPageResultDto;
import com.light.aiszzy.homeworkResult.entity.dto.HomeworkResultAnswerDto;
import com.light.aiszzy.homeworkResult.entity.dto.HomeworkResultDto;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkPageResultVo;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultAnswerVo;
import com.light.aiszzy.homeworkResult.mapper.HomeworkPageResultMapper;
import com.light.aiszzy.homeworkResult.mapper.HomeworkResultAnswerMapper;
import com.light.aiszzy.homeworkResult.mapper.HomeworkResultMapper;
import com.light.aiszzy.homeworkResult.service.IHomeworkPageResultService;
import com.light.aiszzy.homeworkResult.service.IHomeworkResultAnswerService;
import com.light.aiszzy.resultUploadFile.bo.DealResultMsgBo;
import com.light.contants.AISzzyConstants;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.StringUtils;
import com.light.user.student.api.StudentApi;
import com.light.user.student.entity.vo.StudentVo;
import com.light.utils.StuQuesResultMqUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
/**
 * 扫描结构每页处理结果实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 15:52:36
 */
@Service
public class HomeworkPageResultServiceImpl extends ServiceImpl<HomeworkPageResultMapper, HomeworkPageResultDto> implements IHomeworkPageResultService {

	@Resource
	private HomeworkPageResultMapper homeworkPageResultMapper;

	@Resource
	private StudentApi studentApi;
    @Resource
    private HomeworkResultMapper homeworkResultMapper;
    @Resource
    private HomeworkResultAnswerMapper homeworkResultAnswerMapper;
    @Resource
    private IHomeworkResultAnswerService homeworkResultAnswerService;

	@Override
	public List<HomeworkPageResultVo> getHomeworkPageResultListByCondition(HomeworkPageResultConditionBo condition) {
        return homeworkPageResultMapper.getHomeworkPageResultListByCondition(condition);
	}

	@Override
	public AjaxResult addHomeworkPageResult(HomeworkPageResultBo homeworkPageResultBo) {
		HomeworkPageResultDto homeworkPageResult = new HomeworkPageResultDto();
		BeanUtils.copyProperties(homeworkPageResultBo, homeworkPageResult);
		homeworkPageResult.setIsDelete(StatusEnum.NOTDELETE.getCode());
		homeworkPageResult.setOid(IdUtil.simpleUUID());
		if(save(homeworkPageResult)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateHomeworkPageResult(HomeworkPageResultBo homeworkPageResultBo) {
		LambdaQueryWrapper<HomeworkPageResultDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkPageResultDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkPageResultDto::getOid, homeworkPageResultBo.getOid());
		HomeworkPageResultDto homeworkPageResult = getOne(lqw);
		Long id = homeworkPageResult.getId();
		if(homeworkPageResult == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(homeworkPageResultBo, homeworkPageResult);
		homeworkPageResult.setId(id);
		if(updateById(homeworkPageResult)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public HomeworkPageResultVo getDetail(String oid) {
		LambdaQueryWrapper<HomeworkPageResultDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkPageResultDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkPageResultDto::getOid, oid);
		HomeworkPageResultDto homeworkPageResult = getOne(lqw);
	    HomeworkPageResultVo homeworkPageResultVo = new HomeworkPageResultVo();
		if(homeworkPageResult != null){
			BeanUtils.copyProperties(homeworkPageResult, homeworkPageResultVo);
		}
		return homeworkPageResultVo;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult dealNoStudent(HomeworkPageResultBo bo) {
        try {
			// 1、参数校验，作业页信息（作业页oid）不为空，学生信息（学生oid）不为空
			if(bo == null || StringUtils.isEmpty(bo.getOid())){
				return AjaxResult.fail("参数错误，作业结果页信息不能为空");
			}
			if(StringUtils.isEmpty(bo.getStuOid())){
				return AjaxResult.fail("参数错误，为指定学生");
			}

			// 2、查询学生信息
			AjaxResult<StudentVo> ajaxResult = studentApi.getDetailByUserOid(bo.getStuOid());
			if(!ajaxResult.getSuccess() || ajaxResult.getData() == null){
				return AjaxResult.fail("未查到指定学生信息，请核实");
			}
			StudentVo studentVo = ajaxResult.getData();

			// 3、查询&修改作业结果页信息
			LambdaQueryWrapper<HomeworkPageResultDto> pageLqw = new LambdaQueryWrapper<>();
			pageLqw.eq(HomeworkPageResultDto::getOid, bo.getOid());
			HomeworkPageResultDto homeworkPageResult = getOne(pageLqw);
			if(homeworkPageResult == null){
				return AjaxResult.fail("未查到当前作业结果页信息，请核实");
			}
			homeworkPageResult.setStuOid(bo.getStuOid());
			homeworkPageResult.setClassId(studentVo.getClassesId());
			homeworkPageResult.setStuName(studentVo.getUserRealName());
			// 设置无疑问
			homeworkPageResult.setIsDoubt(AISzzyConstants.DoubtStatus.NO.getType());

			// 2、更新作业结果信息，根据作业oid更新：学生信息、class、
			LambdaQueryWrapper<HomeworkResultDto> resultLqw = new LambdaQueryWrapper<>();
			resultLqw.eq(HomeworkResultDto::getOid, homeworkPageResult.getHomeworkResultOid());
			resultLqw.eq(HomeworkResultDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
			HomeworkResultDto homeworkResultDto = homeworkResultMapper.selectOne(resultLqw);
			if(homeworkResultDto == null) {
				return AjaxResult.fail("未查到当前作业结果信息，请核实");
			}
			// 设置学生结果信息
			homeworkResultDto.setStuOid(studentVo.getUserOid());
			homeworkResultDto.setStuName(studentVo.getUserRealName());
			homeworkResultDto.setClassId(studentVo.getClassesId());
			homeworkResultDto.setStuNo(studentVo.getStudentNo());

			// 3、更新信息
			// 更新作业结果信息
			homeworkResultMapper.updateById(homeworkResultDto);
			// 更新结果页信息
			homeworkPageResultMapper.updateById(homeworkPageResult);
			// 更新作业结果答案信息
			LambdaUpdateWrapper<HomeworkResultAnswerDto> luw = new LambdaUpdateWrapper<>();
			luw.eq(HomeworkResultAnswerDto::getHomeworkPageResultOid, homeworkPageResult.getOid());
			luw.eq(HomeworkResultAnswerDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
			luw.set(HomeworkResultAnswerDto::getStuOid, studentVo.getUserOid());
			luw.set(HomeworkResultAnswerDto::getStuName, studentVo.getUserRealName());
			luw.set(HomeworkResultAnswerDto::getStuNo, studentVo.getStudentNo());
			luw.set(HomeworkResultAnswerDto::getStuClassId, studentVo.getClassesId());
			// 更新作业结果答案信息
			homeworkResultAnswerMapper.update(null, luw);

			// 4、更新重复的作业结果页疑问项类型
			updateRepeatDoubtTypeByHomeworkOid(homeworkPageResult.getHomeworkOid());

			// 5、查询作业结果页信息，发消息队列
			LambdaUpdateWrapper<HomeworkResultAnswerDto> answerLqw = new LambdaUpdateWrapper<>();
			answerLqw.eq(HomeworkResultAnswerDto::getHomeworkPageResultOid, homeworkPageResult.getOid());
			answerLqw.eq(HomeworkResultAnswerDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
			List<HomeworkResultAnswerDto> answerList = homeworkResultAnswerMapper.selectList(answerLqw);
			if(answerList != null && !answerList.isEmpty()) {
				DealResultMsgBo dealResultMsgBo = new DealResultMsgBo();
				dealResultMsgBo.setHomeworkResult(homeworkResultDto);
				dealResultMsgBo.setHomeworkPageResult(homeworkPageResult);
				dealResultMsgBo.setHomeworkResultAnswerList(answerList);
				StuQuesResultMqUtil.publishMessage(JSON.toJSONString(dealResultMsgBo));
			}
			return AjaxResult.success("保存成功");
		} catch (Exception e){
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult dealRepeat(HomeworkPageResultBo bo) {
		return null;
	}

	@Override
	public int updateRepeatDoubtTypeByHomeworkOid(String homeworkOid) {
		int count = 0;

		if(StringUtils.isNotEmpty(homeworkOid)) {
			// 根据作业oid更新存在重复的作业结果页，设置为疑问项、类型为重复
			count += homeworkPageResultMapper.updateRepeatDoubtTypeByHomeworkOid(homeworkOid);

			// 根据作业oid，更新已经不存在重复的作业结果页，设置为非疑问项、类型为无
			count += homeworkPageResultMapper.updateNoRepeatDoubtTypeByHomeworkOid(homeworkOid);
		}

		return count;
	}


}