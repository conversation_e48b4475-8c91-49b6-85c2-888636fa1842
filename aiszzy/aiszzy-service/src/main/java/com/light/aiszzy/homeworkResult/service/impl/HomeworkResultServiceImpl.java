package com.light.aiszzy.homeworkResult.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.light.core.constants.SystemConstants;
import com.light.core.utils.StringUtils;
import com.light.enums.SzzyRedisKeyEnum;
import com.light.redis.component.RedisComponent;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.homeworkResult.entity.dto.HomeworkResultDto;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultConditionBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultBo;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultVo;
import com.light.aiszzy.homeworkResult.service.IHomeworkResultService;
import com.light.aiszzy.homeworkResult.mapper.HomeworkResultMapper;
import com.light.core.entity.AjaxResult;
/**
 * 校本作业结果表接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@Service
public class HomeworkResultServiceImpl extends ServiceImpl<HomeworkResultMapper, HomeworkResultDto> implements IHomeworkResultService {

	@Resource
	private HomeworkResultMapper homeworkResultMapper;

	@Resource
	private RedisComponent redisComponent;
	
    @Override
	public List<HomeworkResultVo> getHomeworkResultListByCondition(HomeworkResultConditionBo condition) {
        return homeworkResultMapper.getHomeworkResultListByCondition(condition);
	}

	@Override
	public List<HomeworkResultVo> getDuplicateHomeworkResultListByCondition(HomeworkResultConditionBo condition) {

		return homeworkResultMapper.getDuplicateHomeworkResultListByCondition(condition);
	}

	@Override
	public AjaxResult addHomeworkResult(HomeworkResultBo homeworkResultBo) {
		HomeworkResultDto homeworkResult = new HomeworkResultDto();
		BeanUtils.copyProperties(homeworkResultBo, homeworkResult);
		homeworkResult.setIsDelete(StatusEnum.NOTDELETE.getCode());
		homeworkResult.setOid(IdUtil.simpleUUID());
		if(save(homeworkResult)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public AjaxResult updateHomeworkResult(HomeworkResultBo homeworkResultBo) {
		LambdaQueryWrapper<HomeworkResultDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkResultDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkResultDto::getOid, homeworkResultBo.getOid());
		HomeworkResultDto homeworkResult = getOne(lqw);
		Long id = homeworkResult.getId();
		if(homeworkResult == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(homeworkResultBo, homeworkResult);
		homeworkResult.setId(id);
		if(updateById(homeworkResult)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public HomeworkResultVo getDetail(String oid) {
		LambdaQueryWrapper<HomeworkResultDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkResultDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkResultDto::getOid, oid);
		HomeworkResultDto homeworkResult = getOne(lqw);
	    HomeworkResultVo homeworkResultVo = new HomeworkResultVo();
		if(homeworkResult != null){
			BeanUtils.copyProperties(homeworkResult, homeworkResultVo);
		}
		return homeworkResultVo;
	}

	@Override
	public HomeworkResultVo getDetailByHomeworkUserOid(String homeworkOid, String stuOid) {

		//优先从Redis中取值
		String  redisKey = SzzyRedisKeyEnum.HOMEWORK_STU.getValue() + homeworkOid + SystemConstants.SEPERATOR_COLON + stuOid + SystemConstants.SEPERATOR_COLON;
		Object object = redisComponent.get(redisKey);
		if (null != object) {
			HomeworkResultVo homeworkResultVo = JSON.parseObject(JSONUtil.toJsonStr(object), HomeworkResultVo.class);

			if (homeworkResultVo != null && StringUtils.isNotBlank(homeworkResultVo.getHomeworkOid())) {
				return homeworkResultVo;
			}
		}

		LambdaQueryWrapper<HomeworkResultDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkResultDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkResultDto::getHomeworkOid, homeworkOid);
		lqw.eq(HomeworkResultDto::getStuOid, stuOid);
		HomeworkResultDto homeworkResult = getOne(lqw);
		HomeworkResultVo homeworkResultVo = new HomeworkResultVo();
		if(homeworkResult != null){
			BeanUtils.copyProperties(homeworkResult, homeworkResultVo);
			//同步入缓存
			redisComponent.set(redisKey, JSON.toJSONString(homeworkResultVo),3600 * 24);
		}
		return homeworkResultVo;
	}

	@Override
	public List<HomeworkResultVo> queryByHomeworkClassId(String homeworkOid, Long classId) {
		//优先从Redis中取值
		String  redisKey = SzzyRedisKeyEnum.HOMEWORK_STU.getValue() + homeworkOid + SystemConstants.SEPERATOR_COLON + classId;
		Map<Object, Object> map = redisComponent.hmget(redisKey);
		if (null != map && !map.isEmpty()) {
			return map.values().stream().map(x-> JSON.parseObject(x.toString(), HomeworkResultVo.class)).collect(Collectors.toList());
		}

		LambdaQueryWrapper<HomeworkResultDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkResultDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkResultDto::getHomeworkOid, homeworkOid);
		lqw.eq(HomeworkResultDto::getClassId, classId);
		List<HomeworkResultDto> homeworkResult = this.baseMapper.selectList(lqw);
		if(CollUtil.isNotEmpty(homeworkResult)){

			//同步入缓存
			Map<String, Object> resultVoMap = homeworkResult.stream().collect(Collectors.toMap(HomeworkResultDto::getOid, JSON::toJSONString));
			redisComponent.hmset(redisKey, resultVoMap,3600 * 24);
			return homeworkResult.stream().map(x -> BeanUtil.toBean(x, HomeworkResultVo.class)).collect(Collectors.toList());
		}
		return Collections.emptyList();
	}
}