package com.light.aiszzy.homework.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 作业疑问项表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-08-02 15:28:43
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("homework_all_doubt")
public class HomeworkAllDoubtDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 作业oid
	 */
	@TableField("homework_oid")
	private String homeworkOid;

	/**
	 * 作业本oid
	 */
	@TableField("homework_book_oid")
	private String homeworkBookOid;

	/**
	 * 学校id
	 */
	@TableField("org_code")
	private String orgCode;

	/**
	 * 年级code
	 */
	@TableField("grade")
	private Integer grade;

	/**
	 * 学科code
	 */
	@TableField("subject")
	private Integer subject;

	/**
	 * 学期  1:上学期  2：下学期
	 */
	@TableField("term")
	private Integer term;

	/**
	 * 剩余疑问项数量
	 */
	@TableField("remainder_doubt_count")
	private Integer remainderDoubtCount;

	/**
	 * 学年，区间前一个，根据当前时间，升年级时间算出值
	 */
	@TableField("year")
	private String year;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新者
	 */
	@TableField("update_by")
	private String updateBy;

}
