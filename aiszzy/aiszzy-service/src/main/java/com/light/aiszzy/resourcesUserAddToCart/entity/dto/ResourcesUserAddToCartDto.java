package com.light.aiszzy.resourcesUserAddToCart.entity.dto;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-09 20:20:22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("resources_user_add_to_cart")
public class ResourcesUserAddToCartDto implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * oid
	 */
	@TableField("oid")
	private String oid;

	/**
	 * 用户oid
	 */
	@TableField("user_oid")
	private String userOid;

	/**
	 * 内部来源oid，question_oid
	 */
	@TableField("question_oid")
	private String questionOid;

	/**
	 * 学校CODE
	 */
	@TableField("org_code")
	private String orgCode;


	/**
	 * 学科code
	 */
	@TableField("subject")
	private Integer subject;

	/**
	 * 题目类型id
	 */
	@TableField("question_type_id")
	private String questionTypeId;

	/**
	 * 题目类型名称
	 */
	@TableField("question_type_name")
	private String questionTypeName;

	/**
	 * 大题号
	 */
	@TableField("big_num")
	private String bigNum;

	/**
	 * 小题号
	 */
	@TableField("small_num")
	private String smallNum;

	/**
	 * 题目url或文字
	 */
	@TableField("ques_body")
	private String quesBody;

	/**
	 * 题目展示类型  0：图片url  1：html文字 
	 */
	@TableField("ques_body_type")
	private Long quesBodyType;

	/**
	 * 创建时间
	 */
	@TableField("create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@TableField("update_time")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@TableField("create_by")
	private String createBy;

	/**
	 * 更新者
	 */
	@TableField("update_by")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@TableField("is_delete")
	private Integer isDelete;

}
