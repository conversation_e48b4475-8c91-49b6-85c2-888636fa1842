package com.light.aiszzy.homeworkResult.service.impl;

import cn.hutool.core.util.IdUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

import com.light.core.enums.StatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import javax.annotation.Resource;

import com.light.aiszzy.homeworkResult.entity.dto.HomeworkResultErrorDetailDto;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultErrorDetailConditionBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultErrorDetailBo;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultErrorDetailVo;
import com.light.aiszzy.homeworkResult.service.IHomeworkResultErrorDetailService;
import com.light.aiszzy.homeworkResult.mapper.HomeworkResultErrorDetailMapper;
import com.light.core.entity.AjaxResult;
import org.springframework.transaction.annotation.Transactional;

/**
 * 校本作业结果详情表(错误试题)接口实现类
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@Service
public class HomeworkResultErrorDetailServiceImpl extends ServiceImpl<HomeworkResultErrorDetailMapper, HomeworkResultErrorDetailDto> implements IHomeworkResultErrorDetailService {

	@Resource
	private HomeworkResultErrorDetailMapper homeworkResultErrorDetailMapper;
	
    @Override
	public List<HomeworkResultErrorDetailVo> getHomeworkResultErrorDetailListByCondition(HomeworkResultErrorDetailConditionBo condition) {
        return homeworkResultErrorDetailMapper.getHomeworkResultErrorDetailListByCondition(condition);
	}

	@Override
	public AjaxResult addHomeworkResultErrorDetail(HomeworkResultErrorDetailBo homeworkResultErrorDetailBo) {
		HomeworkResultErrorDetailDto homeworkResultErrorDetail = new HomeworkResultErrorDetailDto();
		BeanUtils.copyProperties(homeworkResultErrorDetailBo, homeworkResultErrorDetail);
		homeworkResultErrorDetail.setIsDelete(StatusEnum.NOTDELETE.getCode());
		homeworkResultErrorDetail.setOid(IdUtil.simpleUUID());
		if(save(homeworkResultErrorDetail)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult saveBatchByHResultAnswerList(List<HomeworkResultErrorDetailDto> list) {

		if(saveBatch(list)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}

	}

	@Override
	public AjaxResult updateHomeworkResultErrorDetail(HomeworkResultErrorDetailBo homeworkResultErrorDetailBo) {
		LambdaQueryWrapper<HomeworkResultErrorDetailDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkResultErrorDetailDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkResultErrorDetailDto::getOid, homeworkResultErrorDetailBo.getOid());
		HomeworkResultErrorDetailDto homeworkResultErrorDetail = getOne(lqw);
		Long id = homeworkResultErrorDetail.getId();
		if(homeworkResultErrorDetail == null){
			return AjaxResult.fail("保存失败");
		}
		BeanUtils.copyProperties(homeworkResultErrorDetailBo, homeworkResultErrorDetail);
		homeworkResultErrorDetail.setId(id);
		if(updateById(homeworkResultErrorDetail)){
			return AjaxResult.success("保存成功");
		}else{
			return AjaxResult.fail("保存失败");
		}
	}

	@Override
	public HomeworkResultErrorDetailVo getDetail(String oid) {
		LambdaQueryWrapper<HomeworkResultErrorDetailDto> lqw = new LambdaQueryWrapper<>();
		lqw.eq(HomeworkResultErrorDetailDto::getIsDelete, StatusEnum.NOTDELETE.getCode());
		lqw.eq(HomeworkResultErrorDetailDto::getOid, oid);
		HomeworkResultErrorDetailDto homeworkResultErrorDetail = getOne(lqw);
	    HomeworkResultErrorDetailVo homeworkResultErrorDetailVo = new HomeworkResultErrorDetailVo();
		if(homeworkResultErrorDetail != null){
			BeanUtils.copyProperties(homeworkResultErrorDetail, homeworkResultErrorDetailVo);
		}
		return homeworkResultErrorDetailVo;
	}

}