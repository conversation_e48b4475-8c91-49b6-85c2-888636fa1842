package com.light.aiszzy.statistics.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.light.aiszzy.statistics.entity.dto.QuestionStatisticsDto;
import com.light.aiszzy.statistics.entity.bo.QuestionStatisticsConditionBo;
import com.light.aiszzy.statistics.entity.vo.QuestionStatisticsVo;

/**
 * 题目正确率Mapper
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-08-02 18:11:10
 */
public interface QuestionStatisticsMapper extends BaseMapper<QuestionStatisticsDto> {

	List<QuestionStatisticsVo> getQuestionStatisticsListByCondition(QuestionStatisticsConditionBo condition);

}
