package com.light.aiszzy.homeworkResult.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.light.aiszzy.homeworkResult.entity.dto.HomeworkResultErrorDetailDto;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultErrorDetailConditionBo;
import com.light.aiszzy.homeworkResult.entity.bo.HomeworkResultErrorDetailBo;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultErrorDetailVo;
import com.light.core.entity.AjaxResult;

import java.util.List;

/**
 * 校本作业结果详情表(错误试题)接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface IHomeworkResultErrorDetailService extends IService<HomeworkResultErrorDetailDto> {

    List<HomeworkResultErrorDetailVo> getHomeworkResultErrorDetailListByCondition(HomeworkResultErrorDetailConditionBo condition);

	AjaxResult addHomeworkResultErrorDetail(HomeworkResultErrorDetailBo homeworkResultErrorDetailBo);

    AjaxResult saveBatchByHResultAnswerList(List<HomeworkResultErrorDetailDto> list);

	AjaxResult updateHomeworkResultErrorDetail(HomeworkResultErrorDetailBo homeworkResultErrorDetailBo);

    HomeworkResultErrorDetailVo getDetail(String oid);

}

