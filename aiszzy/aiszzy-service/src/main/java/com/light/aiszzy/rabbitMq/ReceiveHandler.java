package com.light.aiszzy.rabbitMq;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.light.aiszzy.apiRequestLog.entity.dto.ApiRequestLogDto;
import com.light.aiszzy.apiRequestLog.mapper.ApiRequestLogMapper;
import com.light.aiszzy.homework.entity.dto.HomeworkClassDto;
import com.light.aiszzy.homework.entity.vo.HomeworkAllDoubtVo;
import com.light.aiszzy.homework.service.IHomeworkAllDoubtService;
import com.light.aiszzy.homework.service.IHomeworkClassService;
import com.light.aiszzy.homeworkResult.entity.dto.HomeworkPageResultDto;
import com.light.aiszzy.homeworkResult.entity.dto.HomeworkResultAnswerDto;
import com.light.aiszzy.homeworkResult.entity.dto.HomeworkResultErrorDetailDto;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkResultVo;
import com.light.aiszzy.homeworkResult.service.IHomeworkResultErrorDetailService;
import com.light.aiszzy.homeworkResult.service.IHomeworkResultService;
import com.light.aiszzy.resultUploadFile.bo.DealResultMsgBo;
import com.light.aiszzy.resultUploadFile.entity.dto.ResultUploadFileDto;
import com.light.aiszzy.resultUploadFile.mapper.ResultUploadFileMapper;
import com.light.aiszzy.statistics.entity.vo.HomeworkClassQuestionStatisticsVo;
import com.light.aiszzy.statistics.entity.vo.QuestionStatisticsVo;
import com.light.aiszzy.statistics.entity.vo.SchoolGradeQuestionStatisticsVo;
import com.light.aiszzy.statistics.service.IHomeworkClassQuestionStatisticsService;
import com.light.aiszzy.statistics.service.IQuestionStatisticsService;
import com.light.aiszzy.statistics.service.ISchoolGradeQuestionStatisticsService;
import com.light.contants.AISzzyConstants;
import com.light.core.constants.SystemConstants;
import com.light.core.enums.StatusEnum;
import com.light.core.utils.StringUtils;
import com.light.enums.CorrectResultEnum;
import com.light.enums.SzzyRedisKeyEnum;
import com.light.redis.component.RedisComponent;
import com.light.utils.ApiRequestLogUtil;
import com.light.utils.StuQuesResultMqUtil;
import com.light.utils.UploadFileMqUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


@Slf4j
@Component
public class ReceiveHandler {

    @Autowired
    private ApiRequestLogMapper apiRequestLogMapper;

    @Autowired
    private ResultUploadFileMapper resultUploadFileMapper;

//    @Autowired
//    private IResultUploadFileDealService iResultUploadFileDealService;

    @Autowired
    private IHomeworkResultErrorDetailService homeworkResultErrorDetailService;

    @Autowired
    private IHomeworkAllDoubtService homeworkAllDoubtService;

    @Autowired
    private IHomeworkClassService HomeworkClassService;

    @Autowired
    private IHomeworkResultService homeworkResultService;
    @Autowired
    private IHomeworkClassQuestionStatisticsService homeworkClassQuestionStatisticsService;

    @Autowired
    private ISchoolGradeQuestionStatisticsService schoolGradeQuestionStatisticsService;

    @Autowired
    private IQuestionStatisticsService questionStatisticsService;

    @Resource
    private RedisComponent redisComponent;

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(name = ApiRequestLogUtil.API_REQUEST_LOG_LOGIN_LOG_QUEUE, durable = "true"),
            exchange = @Exchange(name = ApiRequestLogUtil.API_REQUEST_LOG_EXCHANGE,
                    ignoreDeclarationExceptions = "true"),
            key = {ApiRequestLogUtil.API_REQUEST_LOG_LOGIN_LOG_QUEUE}
    ))
    public void receiveApiRequestLog(String msg) {
        try {
            ApiRequestLogDto dto = JSONUtil.toBean(msg, ApiRequestLogDto.class);
            apiRequestLogMapper.insert(dto);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(name = UploadFileMqUtil.UPLADE_FILE_QUEUE, durable = "true"),
            exchange = @Exchange(name = UploadFileMqUtil.UPLADE_FILE_EXCHANGE,
                    ignoreDeclarationExceptions = "true"),
            key = {UploadFileMqUtil.UPLADE_FILE_QUEUE}
    ))
    public void receiveUploadFile(String msg) {
        try {
            ResultUploadFileDto dto = JSONUtil.toBean(msg, ResultUploadFileDto.class);
            //iResultUploadFileDealService.dealResultUploadFileByMsg(dto);
            resultUploadFileMapper.insert(dto);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(name = StuQuesResultMqUtil.STU_QUES_RESULT_QUEUE, durable = "true"),
            exchange = @Exchange(name = StuQuesResultMqUtil.STU_QUES_RESULT_EXCHANGE,
                    ignoreDeclarationExceptions = "true"),
            key = {StuQuesResultMqUtil.STU_QUES_RESULT_QUEUE}
    ))
    public void receiveStuQuesResult(String msg) {
        try {
            DealResultMsgBo dealResultMsgBo = JSONUtil.toBean(msg, DealResultMsgBo.class);

            if (null != dealResultMsgBo) {

                if (dealResultMsgBo.getHomeworkPageResult() != null) {

                    HomeworkPageResultDto homeworkPageResultDto = dealResultMsgBo.getHomeworkPageResult();
                    //如果作业存在疑问项，则增加疑问项统计
                    if (AISzzyConstants.DoubtStatus.EXIST.getType().equals(homeworkPageResultDto.getDoubtType()))
                    {
                        dealHomeworkQuestionDoubt(homeworkPageResultDto.getHomeworkOid());
                    }
                }

                List<HomeworkResultAnswerDto> dtoList = dealResultMsgBo.getHomeworkResultAnswerList();

                List<HomeworkResultErrorDetailDto> HomeworkResultErrorDetailDtoList = new ArrayList<>();

                for (HomeworkResultAnswerDto dto : dtoList) {

                    //如果为空或者作业oid为空，则放过
                    if (null == dto || StringUtils.isBlank(dto.getHomeworkOid())) {
                        continue;
                    }

                    //其他题目相关统计
                    if (StringUtils.isNotBlank(dto.getStuOid()) && StringUtils.isNotBlank(dto.getQuestionOid())
                            && StringUtils.isNotBlank(dto.getOrgCode()) && null != dto.getStuClassId()) {
                        //组装学生错题List
                        // 保存分页数据
                        HomeworkResultErrorDetailDto homeworkResultErrorDetail = new HomeworkResultErrorDetailDto();
                        BeanUtils.copyProperties(dto, homeworkResultErrorDetail);
                        homeworkResultErrorDetail.setIsDelete(StatusEnum.NOTDELETE.getCode());
                        homeworkResultErrorDetail.setOid(IdUtil.simpleUUID());
                        HomeworkResultErrorDetailDtoList.add(homeworkResultErrorDetail);

                        //判断题目疑问项统计
                        judgeQuestionDoubt(dto);

                        //作业班级上传表
                        //作业班级信息入库（即刻入缓存、入库）
                        dealHomeworkClass(dto);

                        //作业班级题目统计结果入库（先入Redis，闲时入库）
                        dealHomeworkClassQuestionStatistics(dto);

                        //作业学生统计结果入库（先入Redis，闲时入库）
                        dealHomeworkResultStatistics(dto);

                        //学校年级题目统计结果入库（先入Redis，闲时入库）
                        dealOrgGradeQuestionStatistics(dto);

                        //题目统计结果入库（先入Redis，闲时入库）
                        dealQuestionStatistics(dto);

                    }
                }

                //作业、学生、班级、年级、学校
                //学生错题入库（即刻入库）
                if (!HomeworkResultErrorDetailDtoList.isEmpty()) {
                    homeworkResultErrorDetailService.saveBatchByHResultAnswerList(HomeworkResultErrorDetailDtoList);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    //作业班级上传表
    //作业班级信息入库（即刻入缓存、入库）
    void dealHomeworkClass(HomeworkResultAnswerDto dto)
    {

        if (!HomeworkClassService.isExistHomeworkClassId(dto.getHomeworkOid(),dto.getStuClassId())) {
            HomeworkClassDto HomeworkClassDto = new HomeworkClassDto();
            BeanUtils.copyProperties(dto, HomeworkClassDto);
            if (HomeworkClassService.save(HomeworkClassDto)){
                //优先从Redis中取值
                String  redisKey = SzzyRedisKeyEnum.HOMEWORK_CLASS.getValue() + dto.getHomeworkOid() + SystemConstants.SEPERATOR_COLON + dto.getStuClassId() + SystemConstants.SEPERATOR_COLON;
                //最长时间1天
                redisComponent.set(redisKey,SystemConstants.YES,3600 * 24);
            }
        }
    }


    //作业班级题目统计结果入库（先入Redis，闲时入库）
    void dealHomeworkClassQuestionStatistics(HomeworkResultAnswerDto dto){

        //获取作业班级题目统计信息
        HomeworkClassQuestionStatisticsVo homeworkClassQuestionStatisticsVo = homeworkClassQuestionStatisticsService.getDetailByHomeworkClassQuestion(dto.getHomeworkOid(),dto.getStuClassId(),dto.getQuestionOid());

        //如果为空，则新生成
        if (null == homeworkClassQuestionStatisticsVo || null == homeworkClassQuestionStatisticsVo.getHomeworkOid()){
            homeworkClassQuestionStatisticsVo = new HomeworkClassQuestionStatisticsVo();
            BeanUtils.copyProperties(dto, homeworkClassQuestionStatisticsVo);
        }

        if (CorrectResultEnum.CORRECT.getCode().equals(dto.getIsCorrect()))
        {
            homeworkClassQuestionStatisticsVo.setRightNum(homeworkClassQuestionStatisticsVo.getRightNum()+1);
            homeworkClassQuestionStatisticsVo.setTotalNum(homeworkClassQuestionStatisticsVo.getTotalNum()+1);

        }else if (CorrectResultEnum.WRONG.getCode().equals(dto.getIsCorrect())){
            homeworkClassQuestionStatisticsVo.setWrongNum(homeworkClassQuestionStatisticsVo.getWrongNum()+1);
            homeworkClassQuestionStatisticsVo.setTotalNum(homeworkClassQuestionStatisticsVo.getTotalNum()+1);
        }

        //计算正确率
        homeworkClassQuestionStatisticsVo.setAccuracyRate(homeworkClassQuestionStatisticsVo.getRightNum() * 100/homeworkClassQuestionStatisticsVo.getTotalNum() );

        //优先从Redis中取值
        String  redisKey = SzzyRedisKeyEnum.HOMEWORK_CLASS_QUESTION_STATISTICS.getValue() + dto.getHomeworkOid() + SystemConstants.SEPERATOR_COLON + dto.getStuClassId() + SystemConstants.SEPERATOR_COLON + dto.getQuestionOid() + SystemConstants.SEPERATOR_COLON;
        redisComponent.set(redisKey, JSON.toJSONString(homeworkClassQuestionStatisticsVo),3600 * 24);

        // 设置 作业班级统计集合
        this.dealHomeworkClassStatisticsList(homeworkClassQuestionStatisticsVo);
    }


    /**
     *  设置 作业班级 集合
     * @param vo the statistics  vo
     */
    void dealHomeworkClassStatisticsList(HomeworkClassQuestionStatisticsVo vo){
        //优先从Redis中取值
        String  redisKey = SzzyRedisKeyEnum.HOMEWORK_CLASS_QUESTION_STATISTICS.getValue() + vo.getHomeworkOid() + SystemConstants.SEPERATOR_COLON + vo.getClassId();
        redisComponent.hset(redisKey,vo.getOid(), JSON.toJSONString(vo),3600 * 24);
    }


    //作业学生统计结果入库（先入Redis，闲时入库）
    void dealHomeworkResultStatistics(HomeworkResultAnswerDto dto){

        //获取作业学生统计信息
        HomeworkResultVo homeworkResultVo = homeworkResultService.getDetailByHomeworkUserOid(dto.getHomeworkOid(),dto.getStuOid());
        //如果为空，则忽略
        if (null == homeworkResultVo || null == homeworkResultVo.getHomeworkOid()){
            return;
        }

        if (CorrectResultEnum.CORRECT.getCode().equals(dto.getIsCorrect()))
        {
            homeworkResultVo.setRightNum(homeworkResultVo.getRightNum()+1);
            homeworkResultVo.setTotalNum(homeworkResultVo.getTotalNum()+1);

        }else if (CorrectResultEnum.WRONG.getCode().equals(dto.getIsCorrect())){
            homeworkResultVo.setWrongNum(homeworkResultVo.getWrongNum()+1);
            homeworkResultVo.setTotalNum(homeworkResultVo.getTotalNum()+1);
        }

        //计算正确率
        homeworkResultVo.setAccuracyRate(homeworkResultVo.getRightNum() * 100/homeworkResultVo.getTotalNum() );

        //优先从Redis中取值
        String  redisKey = SzzyRedisKeyEnum.HOMEWORK_STU.getValue() + dto.getHomeworkOid() + SystemConstants.SEPERATOR_COLON + dto.getStuOid() + SystemConstants.SEPERATOR_COLON;
        redisComponent.set(redisKey, JSON.toJSONString(homeworkResultVo),3600 * 24);

        // 设置
        this.dealHomeworkResultClassStatisticsList(homeworkResultVo);
    }

    /**
     *  班级作业 学生统计 集合
     * @param vo the vo
     */
    void dealHomeworkResultClassStatisticsList(HomeworkResultVo vo){
        //优先从Redis中取值
        String  redisKey = SzzyRedisKeyEnum.HOMEWORK_STU.getValue() + vo.getHomeworkOid() + SystemConstants.SEPERATOR_COLON + vo.getClassId() ;
        redisComponent.hset(redisKey,vo.getOid(), JSON.toJSONString(vo),3600 * 24);
    }


    void dealOrgGradeQuestionStatistics(HomeworkResultAnswerDto dto)
    {
        //获取学校年级题目统计信息
        SchoolGradeQuestionStatisticsVo schoolGradeQuestionStatisticsVo = schoolGradeQuestionStatisticsService.getDetailBySchoolGradeQuestion(dto.getOrgCode(),dto.getGrade(),dto.getQuestionOid());
        //如果为空，则新生成
        if (null == schoolGradeQuestionStatisticsVo || null == schoolGradeQuestionStatisticsVo.getGrade()){
            schoolGradeQuestionStatisticsVo = new SchoolGradeQuestionStatisticsVo();
            BeanUtils.copyProperties(dto, schoolGradeQuestionStatisticsVo);
        }

        if (CorrectResultEnum.CORRECT.getCode().equals(dto.getIsCorrect()))
        {
            schoolGradeQuestionStatisticsVo.setRightNum(schoolGradeQuestionStatisticsVo.getRightNum()+1);
            schoolGradeQuestionStatisticsVo.setTotalNum(schoolGradeQuestionStatisticsVo.getTotalNum()+1);

        }else if (CorrectResultEnum.WRONG.getCode().equals(dto.getIsCorrect())){
            schoolGradeQuestionStatisticsVo.setWrongNum(schoolGradeQuestionStatisticsVo.getWrongNum()+1);
            schoolGradeQuestionStatisticsVo.setTotalNum(schoolGradeQuestionStatisticsVo.getTotalNum()+1);
        }

        //计算正确率
        schoolGradeQuestionStatisticsVo.setAccuracyRate(schoolGradeQuestionStatisticsVo.getRightNum() * 100/schoolGradeQuestionStatisticsVo.getTotalNum() );

        //优先从Redis中取值
        String  redisKey = SzzyRedisKeyEnum.ORG_CODE_GRADE_QUESTION_STATISTICS.getValue() + dto.getOrgCode() + SystemConstants.SEPERATOR_COLON + dto.getGrade() + SystemConstants.SEPERATOR_COLON + dto.getQuestionOid() + SystemConstants.SEPERATOR_COLON;
        redisComponent.set(redisKey, JSON.toJSONString(schoolGradeQuestionStatisticsVo),3600 * 24);
    }

    void dealQuestionStatistics(HomeworkResultAnswerDto dto)
    {
        //获取学校年级题目统计信息
        QuestionStatisticsVo questionStatisticsVo = questionStatisticsService.getDetailByQuestionOid(dto.getQuestionOid());
        //如果为空，则新生成
        if (null == questionStatisticsVo || null == questionStatisticsVo.getQuestionOid()){
            questionStatisticsVo = new QuestionStatisticsVo();
            BeanUtils.copyProperties(dto, questionStatisticsVo);
        }

        if (CorrectResultEnum.CORRECT.getCode().equals(dto.getIsCorrect()))
        {
            questionStatisticsVo.setRightNum(questionStatisticsVo.getRightNum()+1);
            questionStatisticsVo.setTotalNum(questionStatisticsVo.getTotalNum()+1);

        }else if (CorrectResultEnum.WRONG.getCode().equals(dto.getIsCorrect())){
            questionStatisticsVo.setWrongNum(questionStatisticsVo.getWrongNum()+1);
            questionStatisticsVo.setTotalNum(questionStatisticsVo.getTotalNum()+1);
        }

        //计算正确率
        questionStatisticsVo.setAccuracyRate(questionStatisticsVo.getRightNum() * 100/questionStatisticsVo.getTotalNum() );

        //优先从Redis中取值
        String  redisKey = SzzyRedisKeyEnum.QUESTION_STATISTICS.getValue() + dto.getOrgCode() + SystemConstants.SEPERATOR_COLON + dto.getGrade() + SystemConstants.SEPERATOR_COLON + dto.getQuestionOid() + SystemConstants.SEPERATOR_COLON;
        redisComponent.set(redisKey, JSON.toJSONString(questionStatisticsVo),3600 * 24);
    }

    //疑问项统计
    void judgeQuestionDoubt(HomeworkResultAnswerDto dto)
    {
        //如果题目存在疑问
        if (AISzzyConstants.DoubtStatus.EXIST.getType().equals(dto.getIsDoubt())) {
            dealHomeworkQuestionDoubt(dto.getHomeworkOid());
        }
    }

    //疑问项统计
    void dealHomeworkQuestionDoubt(String homeworkOid)
    {
        HomeworkAllDoubtVo homeworkAllDoubtVo = homeworkAllDoubtService.getDetailByHomeworkOid(homeworkOid);
        homeworkAllDoubtVo.setRemainderDoubtCount(homeworkAllDoubtVo.getRemainderDoubtCount()+1);

        //优先从Redis中取值
        String  redisKey = SzzyRedisKeyEnum.HOMEWORK_All_DOUBT.getValue() + homeworkOid + SystemConstants.SEPERATOR_COLON;
        //同步入缓存
        redisComponent.set(redisKey, JSON.toJSONString(homeworkAllDoubtVo),3600 * 24);

    }


}
