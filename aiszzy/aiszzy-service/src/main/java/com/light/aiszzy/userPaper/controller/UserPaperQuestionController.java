package com.light.aiszzy.userPaper.controller;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.light.aiszzy.practiceBook.entity.vo.PracticeBookQuestionVo;
import com.light.aiszzy.schoolResourcesQuestion.entity.vo.SchoolResourcesQuestionVo;
import com.light.aiszzy.schoolResourcesQuestion.service.ISchoolResourcesQuestionService;
import com.light.aiszzy.xkw.xkwKnowledgePoints.entity.vo.XkwKnowledgePointsVo;
import com.light.aiszzy.xkw.xkwKnowledgePoints.service.IXkwKnowledgePointsService;
import com.light.core.exception.WarningException;
import io.swagger.annotations.Api;

import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.light.aiszzy.userPaper.entity.bo.UserPaperQuestionConditionBo;
import com.light.aiszzy.userPaper.entity.bo.UserPaperQuestionBo;
import com.light.aiszzy.userPaper.entity.vo.UserPaperQuestionVo;
import com.light.aiszzy.userPaper.service.IUserPaperQuestionService;

import com.light.aiszzy.userPaper.api.UserPaperQuestionApi;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.core.enums.StatusEnum;

/**
 * 资源库试卷表
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:35
 */
@RestController
@Validated
@Api(value = "", tags = "资源库试卷表接口")
public class UserPaperQuestionController implements UserPaperQuestionApi {

    @Autowired
    private IUserPaperQuestionService userPaperQuestionService;

    @Resource
    private ISchoolResourcesQuestionService schoolResourcesQuestionService;

    @Resource
    private IXkwKnowledgePointsService knowledgePointsService;

    public AjaxResult<PageInfo<UserPaperQuestionVo>> getUserPaperQuestionPageListByCondition(@RequestBody UserPaperQuestionConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        PageHelper.startPage(condition.getPageNo(), condition.getPageSize(),com.light.aiszzy.utils.SqlUtil.sanitizeOrderBy(condition.getOrderBy(),condition.getClass()));
        PageInfo<UserPaperQuestionVo> pageInfo = new PageInfo<>(userPaperQuestionService.getUserPaperQuestionListByCondition(condition));
        return AjaxResult.success(pageInfo.getList(), pageInfo.getTotal(), condition.getPageNo(), condition.getPageSize());
    }

    public AjaxResult<List<UserPaperQuestionVo>> getUserPaperQuestionListByCondition(@RequestBody UserPaperQuestionConditionBo condition) {
        condition.setIsDelete(StatusEnum.NOTDELETE.getCode());
        return AjaxResult.success(userPaperQuestionService.getUserPaperQuestionListByCondition(condition));
    }

    @Override
    public AjaxResult<List<UserPaperQuestionVo>> queryByUserPaperOid(@RequestParam("userPaperOid") String userPaperOid) {
        List<UserPaperQuestionVo> data = this.userPaperQuestionService.queryByUserPaperOid(userPaperOid);

        Optional.ofNullable(data).filter(CollUtil::isNotEmpty).ifPresent(x-> {
            // 获取知识点 ID集合
            List<Long> knowledgePointsIdList = x.stream().map(UserPaperQuestionVo::getSchoolResourcesQuestion)
                    .filter(Objects::nonNull)
                    .map(SchoolResourcesQuestionVo::getKnowledgePointsId)
                    .filter(StrUtil::isNotEmpty)
                    .map(e ->
                            StrUtil.splitTrim(e, ",")).flatMap(Collection::stream)
                    .map(Long::parseLong)
                    .collect(Collectors.toList());

            if(CollUtil.isNotEmpty(knowledgePointsIdList)) {
                // 查询知识点
                Map<Long, XkwKnowledgePointsVo> pointsMap = this.knowledgePointsService.queryMapByIdList(knowledgePointsIdList);

                //
                data.stream()
                        // 过滤没有题目信息数据
                        .filter(userPaperQuestionVo -> userPaperQuestionVo.getSchoolResourcesQuestion() != null)
                        // 过滤知识点为空
                        .filter(userPaperQuestionVo -> StrUtil.isNotEmpty(userPaperQuestionVo.getSchoolResourcesQuestion().getKnowledgePointsId()))
                        // 循环赋值知识点名称
                        .forEach(userPaperQuestionVo -> {
                            List<String> knowledgePointsIdStr = StrUtil.splitTrim(userPaperQuestionVo.getSchoolResourcesQuestion().getKnowledgePointsId(), ",");
                            String knowledgePointsNameList = knowledgePointsIdStr.stream().filter(id-> pointsMap.containsKey(Long.parseLong(id)))
                                    .map(id-> pointsMap.get(Long.parseLong(id))).map(XkwKnowledgePointsVo::getName).collect(Collectors.joining(","));
                            userPaperQuestionVo.getSchoolResourcesQuestion().setKnowledgePointsName(knowledgePointsNameList);
                        });
            }

        });
        return AjaxResult.success(data);
    }

    public AjaxResult addUserPaperQuestion(@Validated @RequestBody UserPaperQuestionBo userPaperQuestionBo) {
        return userPaperQuestionService.addUserPaperQuestion(userPaperQuestionBo);
    }

    public AjaxResult updateUserPaperQuestion(@Validated @RequestBody UserPaperQuestionBo userPaperQuestionBo) {
        if (null == userPaperQuestionBo.getOid()) {
            return AjaxResult.fail("oid不能为空");
        }
        return userPaperQuestionService.updateUserPaperQuestion(userPaperQuestionBo);
    }

    public AjaxResult<UserPaperQuestionVo> getDetail(@NotNull(message = "请选择数据") String oid) {
        return AjaxResult.success(userPaperQuestionService.getDetail(oid));
    }

    public AjaxResult delete(@NotNull(message = "请选择需要删除的数据") String oid) {
            UserPaperQuestionBo userPaperQuestionBo = new UserPaperQuestionBo();
            userPaperQuestionBo.setOid(oid);
            userPaperQuestionBo.setIsDelete(StatusEnum.ISDELETE.getCode());
        return userPaperQuestionService.updateUserPaperQuestion(userPaperQuestionBo);
    }

    @Override
    public AjaxResult<UserPaperQuestionVo> getOrInitQuestionInfoByOid(@RequestBody UserPaperQuestionBo bo) {
        String oid = bo.getOid();
        if(StrUtil.isEmpty(oid)) {
            return AjaxResult.fail("OID不能为空");
        }
        String position = bo.getPosition();
        if(StrUtil.isEmpty(position)) {
            return AjaxResult.fail("位置不能为空");
        }
        UserPaperQuestionVo vo = Optional.ofNullable(this.userPaperQuestionService.queryByOid(oid)).orElseThrow(()-> new WarningException("题目不存在"));
        String schoolResourceQuestionOid = vo.getSchoolResourceQuestionOid();

        // 题目信息不为空 && position 没有进行更改
        if(StrUtil.isNotEmpty(schoolResourceQuestionOid) && position.equalsIgnoreCase(vo.getPosition())) {
            SchoolResourcesQuestionVo schoolResourcesQuestionVo = this.schoolResourcesQuestionService.getDetail(schoolResourceQuestionOid);
            vo.setSchoolResourcesQuestion(schoolResourcesQuestionVo);
            vo.setSchoolResourceQuestionOid(schoolResourcesQuestionVo.getOid());
            return AjaxResult.success(vo);
        }
        UserPaperQuestionVo practiceBookQuestionVo = this.userPaperQuestionService.processAndUpdateQuestionByPositionImg(vo, vo.getPosition());
        return AjaxResult.success(practiceBookQuestionVo);

    }

    @Override
    public AjaxResult addQuestionByPosition(@RequestBody UserPaperQuestionBo bo) {
        String userPaperPageOid = bo.getUserPaperPageOid();
        if(StrUtil.isEmpty(userPaperPageOid)) {
            return AjaxResult.fail("用户校本不能为空");
        }
        String position = bo.getPosition();
        if(StrUtil.isEmpty(position)) {
            return AjaxResult.fail("坐标不能为空");
        }
        AjaxResult<UserPaperQuestionVo> ajaxResult = this.userPaperQuestionService.addUserPaperQuestion(bo);
        if(ajaxResult.isFail()) {
            return ajaxResult;
        }
        UserPaperQuestionVo data = ajaxResult.getData();
        UserPaperQuestionVo userPaperQuestionVo = this.userPaperQuestionService.processAndUpdateQuestionByPositionImg(data, position);
        return AjaxResult.success(userPaperQuestionVo);
    }


    @Override
    public AjaxResult cancelUserQuestion(@RequestBody UserPaperQuestionBo bo) {
        String oid = bo.getOid();
        if(StrUtil.isEmpty(oid)) {
            return AjaxResult.fail("题目 OID 不能为空");
        }
        return this.userPaperQuestionService.cancelUserPaperQuestion(bo);
    }

    @Override
    public AjaxResult markUserQuestion(@RequestParam String oid){
        if(StringUtil.isEmpty(oid)){
            return AjaxResult.fail("题目OID不能为空");
        }

        return this.userPaperQuestionService.markUserPaperQuestion(oid);
    }

    @Override
    public AjaxResult queryByUserPaperPageOid(@RequestParam("userPaperPageOid") String userPaperPageOid) {
        List<UserPaperQuestionVo> list = this.userPaperQuestionService.queryByUserPaperPageOid(userPaperPageOid);
        return AjaxResult.success(list);
    }

    @Override
    public AjaxResult reFetchQuestionInfoByOid(@RequestBody UserPaperQuestionBo bo) {
        String oid = bo.getOid();
        String position = bo.getPosition();
        // 更新题目题目信息
        UserPaperQuestionVo userPaperQuestionVo = this.userPaperQuestionService.processAndUpdateQuestionByPositionImg(oid,  position);
        return AjaxResult.success(userPaperQuestionVo);
    }
}
