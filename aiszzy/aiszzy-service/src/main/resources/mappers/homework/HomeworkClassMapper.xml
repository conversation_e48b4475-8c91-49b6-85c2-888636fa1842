<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.homework.mapper.HomeworkClassMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.homework.entity.dto.HomeworkClassDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="homeworkOid" column="homework_oid"/>
        <result property="orgCode" column="org_code"/>
        <result property="classId" column="class_id"/>
        <result property="className" column="class_name"/>
        <result property="subject" column="subject"/>
        <result property="grade" column="grade"/>
        <result property="term" column="term"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

	<select id="getHomeworkClassListByCondition" resultType="com.light.aiszzy.homework.entity.vo.HomeworkClassVo">
		select t.* from (
			select a.* from homework_class as a
		) t
	    <where>
			<if test="id != null">and id = #{id}</if>
				<if test="oid != null and oid != ''">and oid = #{oid}</if>
				<if test="homeworkOid != null and homeworkOid != ''">and homework_oid = #{homeworkOid}</if>
				<if test="orgCode != null and orgCode != ''">and org_code = #{orgCode}</if>
				<if test="classId != null">and class_id = #{classId}</if>
			<if test="classIds != null">
				and class_id in
				<foreach collection="classIds.split(',')" open="(" close=")" separator="," item="item">
					${item}
				</foreach>
			</if>
				<if test="subject != null">and subject = #{subject}</if>
				<if test="grade != null">and grade = #{grade}</if>
				<if test="term != null">and term = #{term}</if>
				<if test="createTime != null">and create_time = #{createTime}</if>
				<if test="updateTime != null">and update_time = #{updateTime}</if>
				<if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
				<if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
		    </where>
	</select>
</mapper>