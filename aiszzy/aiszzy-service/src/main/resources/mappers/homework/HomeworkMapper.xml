<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.light.aiszzy.homework.mapper.HomeworkMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.light.aiszzy.homework.entity.dto.HomeworkDto" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="oid" column="oid"/>
        <result property="code" column="code"/>
        <result property="orgCode" column="org_code"/>
        <result property="homeworkName" column="homework_name"/>
        <result property="subject" column="subject"/>
        <result property="grade" column="grade"/>
        <result property="term" column="term"/>
        <result property="isScan" column="is_scan"/>
        <result property="reportState" column="report_state"/>
        <result property="homeworkPdfUrl" column="homework_pdf_url"/>
        <result property="homeworkAnswerPdfUrl" column="homework_answer_pdf_url"/>
        <result property="year" column="year"/>
        <result property="isUse" column="is_use"/>
        <result property="homeworkBookOid" column="homework_book_oid"/>
        <result property="paperJson" column="paper_json"/>
        <result property="qrCodeRect" column="qr_code_rect"/>
        <result property="userIdRect" column="user_id_rect"/>
        <result property="userNameRect" column="user_name_rect"/>
        <result property="questionNum" column="question_num"/>
        <result property="pageNum" column="page_num"/>
        <result property="pageSize" column="page_size"/>
        <result property="status" column="status"/>
        <result property="generateRuleType" column="generate_rule_type"/>
        <result property="sourceType" column="source_type"/>
        <result property="sourceOid" column="source_oid"/>
        <result property="paperWidth" column="paper_width"/>
        <result property="paperHeight" column="paper_height"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createByRealName" column="create_by_real_name"/>
        <result property="updateBy" column="update_by"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>

	<select id="getHomeworkListByCondition" resultType="com.light.aiszzy.homework.entity.vo.HomeworkVo">
        select t.* from (
        select a.id,
        a.oid,
        a.code,
        a.org_code,
        a.homework_name,
        a.subject,
        a.grade,
        a.term,
        a.is_scan,
        a.report_state,
        a.homework_pdf_url,
        a.homework_answer_pdf_url,
        a.`year`,
        a.is_use,
        a.homework_book_oid,
--         a.paper_json,
        a.qr_code_rect,
        a.user_id_rect,
        a.user_name_rect,
        a.question_num,
        a.page_num,
        a.page_size,
        a.status,
        a.generate_rule_type,
        a.source_type,
        a.source_oid,
        a.create_time,
        a.update_time,
        a.create_by,
        a.create_by_real_name,
        a.update_by,
        a.is_delete,
        hb.name as homeworkBookName
        <if test="hasBind != null and hasBind != ''">
            ,b.order_num
        </if>
        from homework as a
        left join homework_book as hb on a.homework_book_oid = hb.oid
        <if test="hasBind != null and hasBind != ''">
            left join homework_book_catalog_info b on a.oid = b.homework_oid and b.is_delete = 0
            <if test="homeworkBookOid != null and homeworkBookOid != ''">
                left join homework_book_catalog c on c.oid = b.homework_book_catalog_oid and c.is_delete = 0
            </if>
            where a.is_delete = 0
            <if test="hasBind == 'yes'">
                and b.homework_book_catalog_oid is not null
            </if>
            <if test="hasBind == 'no'">
                and b.homework_book_catalog_oid is null
            </if>
        </if>
        <if test="homeworkBookCatalogOid != null and homeworkBookCatalogOid != ''">
            and b.homework_book_catalog_oid = #{homeworkBookCatalogOid}
        </if>
        <if test="homeworkBookOid != null and homeworkBookOid != ''">
            and c.homework_book_oid = #{homeworkBookOid}
        </if>
        ) t
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="oid != null and oid != ''">and oid = #{oid}</if>
            <if test="code != null and code != ''">and code = #{code}</if>
            <if test="orgCode != null and orgCode != ''">and org_code = #{orgCode}</if>
            <if test="homeworkName != null and homeworkName != ''">and homework_name like  CONCAT('%', #{homeworkName}, '%')</if>
            <if test="subject != null">and subject = #{subject}</if>
            <if test="grade != null">and grade = #{grade}</if>
            <if test="term != null">and term = #{term}</if>
            <if test="isScan != null">and is_scan = #{isScan}</if>
            <if test="reportState != null">and report_state = #{reportState}</if>
            <if test="homeworkPdfUrl != null and homeworkPdfUrl != ''">and homework_pdf_url = #{homeworkPdfUrl}</if>
            <if test="homeworkAnswerPdfUrl != null and homeworkAnswerPdfUrl != ''">and homework_answer_pdf_url =
                #{homeworkAnswerPdfUrl}
            </if>
            <if test="isGeneratePdf != null">
                <if test="isGeneratePdf == 0">and homework_answer_pdf_url is null</if>
                <if test="isGeneratePdf == 1">and homework_answer_pdf_url  is not null</if>
            </if>
            <if test="year != null and year != ''">and year = #{year}</if>
            <if test="isUse != null">and is_use = #{isUse}</if>
            <if test="homeworkBookOid != null and homeworkBookOid != ''">and homework_book_oid = #{homeworkBookOid}</if>
            <if test="paperJson != null and paperJson != ''">and paper_json = #{paperJson}</if>
            <if test="questionNum != null">and question_num = #{questionNum}</if>
            <if test="pageNum != null">and page_num = #{pageNum}</if>
            <if test="pageSizeSearch != null">and page_size = #{pageSizeSearch}</if>
            <if test="status != null">and status = #{status}</if>
            <if test="generateRuleType != null">and generate_rule_type = #{generateRuleType}</if>
            <if test="sourceType != null">and source_type = #{sourceType}</if>
            <if test="sourceOid != null and sourceOid != ''">and source_oid = #{sourceOid}</if>
            <if test="startDate != null">and create_time >= #{startDate}</if>
            <if test="endDate != null">and create_time &lt;= #{endDate}</if>
            <if test="createTime != null">and create_time = #{createTime}</if>
            <if test="updateTime != null">and update_time = #{updateTime}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="isDelete != null">and is_delete = #{isDelete}</if>
            <if test="createByRealName != null and createByRealName != ''">and create_by_real_name = #{createByRealName}</if>
        </where>
        <if test="orderBy != null and orderBy != ''">
            order by ${orderBy}
        </if>
    </select>

    <select id="getHomeworkResultPageListByCondition" resultType="com.light.aiszzy.homework.entity.vo.HomeworkVo">
        select t.* from (
        select a.id,
        a.oid,
        a.code,
        a.org_code,
        a.homework_name,
        a.subject,
        a.grade,
        a.term,
        a.is_scan,
        a.report_state,
        a.homework_pdf_url,
        a.homework_answer_pdf_url,
        a.`year`,
        a.is_use,
        a.homework_book_oid,
--         a.paper_json,
        a.qr_code_rect,
        a.user_id_rect,
        a.user_name_rect,
        a.question_num,
        a.page_num,
        a.page_size,
        a.status,
        a.generate_rule_type,
        a.source_type,
        a.source_oid,
        a.create_time,
        a.update_time,
        a.create_by,
        a.create_by_real_name,
        a.update_by,
        a.is_delete,
        GROUP_CONCAT(b.class_id) classIds,
        GROUP_CONCAT(concat(b.class_id,'_',b.class_name)) classNames
        ,ifnull(had.remainder_doubt_count,0)

        from homework_class b
            left join  homework as a on b.homework_oid = a.oid
            left join  homework_all_doubt as had on had.homework_oid = a.oid
        <where>
            <if test="classId != null">  and b.class_id = #{classId} </if>
            <if test="classIds != null">
                and b.class_id in
                <foreach collection="classIds.split(',')" open="(" close=")" separator="," item="item">
                    ${item}
                </foreach>
            </if>
        </where>
        GROUP BY  b.class_id
        ) t
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="oid != null and oid != ''">and oid = #{oid}</if>
            <if test="code != null and code != ''">and code = #{code}</if>
            <if test="orgCode != null and orgCode != ''">and org_code = #{orgCode}</if>
            <if test="homeworkName != null and homeworkName != ''">and homework_name like  CONCAT('%', #{homeworkName}, '%')</if>
            <if test="subject != null">and subject = #{subject}</if>
            <if test="grade != null">and grade = #{grade}</if>
            <if test="term != null">and term = #{term}</if>
            <if test="isScan != null">and is_scan = #{isScan}</if>
            <if test="reportState != null">and report_state = #{reportState}</if>
            <if test="homeworkPdfUrl != null and homeworkPdfUrl != ''">and homework_pdf_url = #{homeworkPdfUrl}</if>
            <if test="homeworkAnswerPdfUrl != null and homeworkAnswerPdfUrl != ''">and homework_answer_pdf_url =
                #{homeworkAnswerPdfUrl}
            </if>
            <if test="isGeneratePdf != null">
                <if test="isGeneratePdf == 0">and homework_answer_pdf_url is null</if>
                <if test="isGeneratePdf == 1">and homework_answer_pdf_url  is not null</if>
            </if>
            <if test="year != null and year != ''">and year = #{year}</if>
            <if test="isUse != null">and is_use = #{isUse}</if>
            <if test="homeworkBookOid != null and homeworkBookOid != ''">and homework_book_oid = #{homeworkBookOid}</if>
            <if test="paperJson != null and paperJson != ''">and paper_json = #{paperJson}</if>
            <if test="questionNum != null">and question_num = #{questionNum}</if>
            <if test="pageNum != null">and page_num = #{pageNum}</if>
            <if test="pageSizeSearch != null">and page_size = #{pageSizeSearch}</if>
            <if test="status != null">and status = #{status}</if>
            <if test="generateRuleType != null">and generate_rule_type = #{generateRuleType}</if>
            <if test="sourceType != null">and source_type = #{sourceType}</if>
            <if test="sourceOid != null and sourceOid != ''">and source_oid = #{sourceOid}</if>
            <if test="startDate != null">and create_time >= #{startDate}</if>
            <if test="endDate != null">and create_time &lt;= #{endDate}</if>
            <if test="createTime != null">and create_time = #{createTime}</if>
            <if test="updateTime != null">and update_time = #{updateTime}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="isDelete != null">and is_delete = #{isDelete}</if>
            <if test="createByRealName != null and createByRealName != ''">and create_by_real_name = #{createByRealName}</if>

         </where>
        <if test="orderBy != null and orderBy != ''">
            order by ${orderBy}
        </if>
    </select>

    <select id="listForBind" resultType="com.light.aiszzy.homework.entity.vo.HomeworkVo">
        select t.* from (
        select a.* from homework as a
        ) t
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="oid != null and oid != ''">and oid = #{oid}</if>
            <if test="code != null and code != ''">and code = #{code}</if>
            <if test="orgCode != null and orgCode != ''">and org_code = #{orgCode}</if>
            <if test="homeworkName != null and homeworkName != ''">and homework_name like  CONCAT('%', #{homeworkName}, '%')</if>
            <if test="subject != null">and subject = #{subject}</if>
            <if test="grade != null">and grade = #{grade}</if>
            <if test="term != null">and term = #{term}</if>
            <if test="isScan != null">and is_scan = #{isScan}</if>
            <if test="reportState != null">and report_state = #{reportState}</if>
            <if test="homeworkPdfUrl != null and homeworkPdfUrl != ''">and homework_pdf_url = #{homeworkPdfUrl}</if>
            <if test="homeworkAnswerPdfUrl != null and homeworkAnswerPdfUrl != ''">and homework_answer_pdf_url =
                #{homeworkAnswerPdfUrl}
            </if>
            <if test="isGeneratePdf != null">
                <if test="isGeneratePdf == 0">and homework_answer_pdf_url is null</if>
                <if test="isGeneratePdf == 1">and homework_answer_pdf_url  is not null</if>
            </if>
            <if test="year != null and year != ''">and year = #{year}</if>
            <if test="isUse != null">and is_use = #{isUse}</if>
            <if test="homeworkBookOid != null and homeworkBookOid != ''">and homework_book_oid = #{homeworkBookOid}</if>
            <if test="paperJson != null and paperJson != ''">and paper_json = #{paperJson}</if>
            <if test="questionNum != null">and question_num = #{questionNum}</if>
            <if test="pageNum != null">and page_num = #{pageNum}</if>
            <if test="pageSizeSearch != null">and page_size = #{pageSizeSearch}</if>
            <if test="status != null">and status = #{status}</if>
            <if test="generateRuleType != null">and generate_rule_type = #{generateRuleType}</if>
            <if test="sourceType != null">and source_type = #{sourceType}</if>
            <if test="sourceOid != null and sourceOid != ''">and source_oid = #{sourceOid}</if>
            <if test="startDate != null">and create_time >= #{startDate}</if>
            <if test="endDate != null">and create_time &lt;= #{endDate}</if>
            <if test="createTime != null">and create_time = #{createTime}</if>
            <if test="updateTime != null">and update_time = #{updateTime}</if>
            <if test="createBy != null and createBy != ''">and create_by = #{createBy}</if>
            <if test="updateBy != null and updateBy != ''">and update_by = #{updateBy}</if>
            <if test="isDelete != null">and is_delete = #{isDelete}</if>
            <if test="createByRealName != null and createByRealName != ''">and create_by_real_name = #{createByRealName}</if>
        </where>
    </select>

</mapper>