package com.light.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.html2pdf.resolver.font.DefaultFontProvider;
import com.itextpdf.io.font.FontProgram;
import com.itextpdf.io.font.FontProgramFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.font.FontProvider;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.io.*;
import java.net.URL;

public class HtmlToPdfUtil {
    private static FontProvider fontProvider = new DefaultFontProvider();

    static {
        try {
            File file = new File("simsun.ttc");
            if (!file.exists()) {
                Resource resource = new ClassPathResource("font/simsun.ttc");
                IoUtil.copy(resource.getInputStream(), new FileOutputStream(file));
            }
            String font = file.getAbsolutePath() + ",0";
            FontProgram fontProgram = FontProgramFactory.createFont(font, false);

            fontProvider.addFont(fontProgram);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static byte[] exportPdfA4(String htmlStr) {
        try {
            ByteArrayOutputStream output = new ByteArrayOutputStream();
            PdfWriter writer = new PdfWriter(output);
            PdfDocument pdf = new PdfDocument(writer);
            pdf.setDefaultPageSize(PageSize.A4);
            ConverterProperties properties = new ConverterProperties();
            properties.setFontProvider(fontProvider);
            HtmlConverter.convertToPdf(htmlStr, pdf, properties);

            return output.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static byte[] exportPdfA3(String htmlStr) {
        try {
            ByteArrayOutputStream output = new ByteArrayOutputStream();
            PdfWriter writer = new PdfWriter(output);
            PdfDocument pdf = new PdfDocument(writer);

            PageSize pageSize = new PageSize(PageSize.A3.getHeight(), PageSize.A3.getWidth());
            pdf.setDefaultPageSize(pageSize);
            ConverterProperties properties = new ConverterProperties();
            properties.setFontProvider(fontProvider);
            HtmlConverter.convertToPdf(htmlStr, pdf, properties);

            return output.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


}

