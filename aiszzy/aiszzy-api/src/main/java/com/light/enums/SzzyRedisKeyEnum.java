package com.light.enums;

import cn.hutool.core.util.StrUtil;
import com.light.redis.enums.RedisKeyEnum;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2019-10-21
 */
public enum SzzyRedisKeyEnum {

    //homework class Statistics的Redis key
    HOMEWORK_CLASS("homework_class:"),

    //homework stu的Redis key
    HOMEWORK_STU("homework_stu:"),

    //homework 疑问项的Redis key
    HOMEWORK_All_DOUBT("homework_all_doubt:"),

    //学校 年级 题目的Redis key
    ORG_CODE_GRADE_QUESTION_STATISTICS("org_code_grade_question_statistics:"),
    //homework class question的Redis key
    HOMEWORK_CLASS_QUESTION_STATISTICS("homework_class_question_statistics:"),

    //题目的Redis key
    QUESTION_STATISTICS("question_statistics:"),

    ;

    private final String value;

    SzzyRedisKeyEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static String getValueByKey(String key) {
        SzzyRedisKeyEnum[] enums = values();
        for(SzzyRedisKeyEnum e:enums) {
            if(key.equals(e.name())) {
                return e.getValue();
            }
        }
        return null;
     }


    public String getKey(Map<String, String> param) {
        final String format = StrUtil.format(this.value, param);
        return format;
    }

    public static String getSmsValidateCodeNumKey(String sendKey){
        if(StrUtil.isEmpty(sendKey)){
            return null;
        }
        return sendKey.concat(":check");
    }

}
