package com.light.aiszzy.homework.entity.bo;



import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 作业本目录关联作业
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@Data
public class HomeworkBookCatalogInfoBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 作业本目录关联作业id
	 */
	@ApiModelProperty("作业本目录关联作业id")
	private Long homeworkBookCatalogInfoId;

	/**
	 * id
	 */
	@ApiModelProperty("id")
	private Long id;
	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;
	private String homeworkBookOid;

	/**
	 * 拖动oid
	 */
	private String dragOid;
	/**
	 * 拖动前一个oid
	 */
	private String dragPreOid;
	/**
	 * 目录oid
	 */
	@ApiModelProperty("目录oid")
	private String homeworkBookCatalogOid;

	/**
	 * 目录oids
	 */
	@ApiModelProperty("目录oids")
	private String homeworkBookCatalogOids;
	/**
	 * 作业oid
	 */
	@ApiModelProperty("作业oid")
	private String homeworkOid;
	/**
	 * 排序
	 */
	@ApiModelProperty("排序")
	private Integer orderNum;
	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;
	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;
	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;
	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
