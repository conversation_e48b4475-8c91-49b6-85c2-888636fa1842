package com.light.aiszzy.homework.service;


import com.light.aiszzy.homework.entity.vo.HomeworkQuestionVo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.homework.api.HomeworkQuestionApi;
import com.light.aiszzy.homework.entity.bo.HomeworkQuestionBo;
import com.light.aiszzy.homework.entity.bo.HomeworkQuestionConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 校本作业题目信息接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:34
 */
@FeignClient(contextId = "homeworkQuestionApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = HomeworkQuestionApiService.HomeworkQuestionApiFallbackFactory.class)
@Component
public interface HomeworkQuestionApiService  extends HomeworkQuestionApi {

	@Component
	class HomeworkQuestionApiFallbackFactory implements FallbackFactory<HomeworkQuestionApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(HomeworkQuestionApiService.HomeworkQuestionApiFallbackFactory.class);
		@Override
		public HomeworkQuestionApiService create(Throwable cause) {
			HomeworkQuestionApiService.HomeworkQuestionApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new HomeworkQuestionApiService() {
				public AjaxResult getHomeworkQuestionPageListByCondition(HomeworkQuestionConditionBo condition){
					return AjaxResult.fail("校本作业题目信息查询失败");
				}
				public AjaxResult getHomeworkQuestionListByCondition(HomeworkQuestionConditionBo condition){
					return AjaxResult.fail("校本作业题目信息查询失败");
				}

				@Override
				public AjaxResult<List<HomeworkQuestionVo>> commentList(HomeworkQuestionConditionBo condition) {
					return AjaxResult.fail("讲评查询所有作业题目信息");
				}

				public AjaxResult addHomeworkQuestion(HomeworkQuestionBo homeworkQuestionBo){
					return AjaxResult.fail("校本作业题目信息新增失败");
				}

				public AjaxResult updateHomeworkQuestion(HomeworkQuestionBo homeworkQuestionBo){
					return AjaxResult.fail("校本作业题目信息更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("校本作业题目信息获取详情失败");
				}

				@Override
				public AjaxResult<HomeworkQuestionVo> detailSimilar(String oid) {
					return AjaxResult.fail("查询作业题目信息详情补相似题失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("校本作业题目信息删除失败");
				}

				@Override
				public AjaxResult changeQuestion(String oid, String oldQuestionOid, String newQuestionOid) {
					return AjaxResult.fail("作业换题失败");
				}

				@Override
				public AjaxResult deleteQuestion(String oid, String questionOid) {
					return AjaxResult.fail("作业删除题目失败");
				}
			};
		}
	}
}