package com.light.aiszzy.homework.api;

import com.github.pagehelper.PageInfo;
import com.light.aiszzy.resourcesUserAddToCart.entity.bo.ResourcesUserAddToCartBo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.homework.entity.vo.HomeworkVo;
import com.light.aiszzy.homework.entity.bo.HomeworkBo;
import com.light.aiszzy.homework.entity.bo.HomeworkConditionBo;
import com.light.log.annotation.OperationLogAnnotation;
import com.light.log.constants.OperationLogConstants;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 作业表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
public interface HomeworkApi {

    /**
     * 查询作业表列表
     *
     * <AUTHOR>
     * @date 2025-07-07 17:24:33
     */
    @PostMapping("/homework/pageList")
    @ApiOperation(value = "分页查询作业表", httpMethod = "POST")
    AjaxResult<PageInfo<HomeworkVo>> getHomeworkPageListByCondition(@RequestBody HomeworkConditionBo condition);

    /**
     * 查询所有作业表列表
     *
     * <AUTHOR>
     * @date 2025-07-07 17:24:33
     */
    @PostMapping("/homework/list")
    @ApiOperation(value = "查询所有作业表", httpMethod = "POST")
    AjaxResult<List<HomeworkVo>> getHomeworkListByCondition(@RequestBody HomeworkConditionBo condition);


    @PostMapping("/homework/listForTeacher")
    @ApiOperation(value = "根据作业本查看老师对应作业", httpMethod = "POST")
    AjaxResult listForTeacher(@RequestBody HomeworkConditionBo condition);

    @PostMapping("/homework/listResult")
    @ApiOperation(value = "查询作业扫描结果", httpMethod = "POST")
    AjaxResult<PageInfo<HomeworkVo>> getHomeworkResultPageListByCondition(@RequestBody HomeworkConditionBo condition);

    @PostMapping("/homework/listForBind")
    @ApiOperation(value = "查所有绑定和未绑定作业", httpMethod = "POST")
    AjaxResult listForBind(@RequestBody HomeworkConditionBo condition);

    /**
     * 查询作业列表
     *
     * <AUTHOR>
     * @date 2025-03-28 16:01:05
     */
    @PostMapping("/homework/listBindAndNoBind")
    @ApiOperation(value = "查所有绑定和未绑定作业", httpMethod = "POST")
    AjaxResult listBindAndNoBind(@RequestBody HomeworkConditionBo condition);

    /**
     * 生成作业code
     *
     * <AUTHOR>
     * @date 2025-07-07 17:24:33
     */
    @PostMapping("/homework/generateCode")
    @ApiOperation(value = "生成作业code", httpMethod = "POST")
    String generateCode();

    /**
     * 新增作业表
     *
     * <AUTHOR>
     * @date 2025-07-07 17:24:33
     */
    @PostMapping("/homework/add")
    @ApiOperation(value = "新增作业表", httpMethod = "POST")
    AjaxResult addHomework(@Validated @RequestBody HomeworkBo homeworkBo);

    /**
     * 批量修改作业状态
     *
     * @param homeworkBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2025-03-28 16:01:05
     */
    @PostMapping("/homework/batchUpdateStatus")
    @ApiOperation(value = "批量修改作业状态", httpMethod = "POST")
    AjaxResult batchUpdateStatus(@Validated @RequestBody HomeworkBo homeworkBo);

    /**
     * 修改作业表
     *
     * @param homeworkBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2025-07-07 17:24:33
     */
    @PostMapping("/homework/update")
    @ApiOperation(value = "修改作业表", httpMethod = "POST")
    AjaxResult updateHomework(@Validated @RequestBody HomeworkBo homeworkBo);


    /**
     * 查询作业表详情
     *
     * @param oid
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2025-07-07 17:24:33
     */
    @GetMapping("/homework/detail")
    @ApiOperation(value = "查询作业表详情", httpMethod = "GET")
    AjaxResult<HomeworkVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

    /**
     * 删除作业表
     *
     * @param oid
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2025-07-07 17:24:33
     */
    @GetMapping("/homework/delete")
    @ApiOperation(value = "删除作业表", httpMethod = "GET")
    @ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
    AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);

      /**
     * 试题篮作业
     *
     * @param bo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2025-07-07 17:24:33
     */
    @PostMapping("/homework/cartHomework")
    @ApiOperation(value = "试题篮作业", httpMethod = "POST")
    AjaxResult<HomeworkVo> cartHomework(@RequestBody ResourcesUserAddToCartBo bo);

    /**
     * 复制作业
     *
     * @param homeworkBo
     * @return
     * @returnType AjaxResult
     * <AUTHOR>
     * @date 2025-07-07 17:24:33
     */
    @GetMapping("/homework/copyHomework")
    @ApiOperation(value = "复制作业", httpMethod = "GET")
    @ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
    AjaxResult<HomeworkVo> copyHomework(@Validated @RequestBody HomeworkBo homeworkBo);

    @PostMapping("/homework/originalHomework")
    @ApiOperation(value = "生成原题作业", httpMethod = "POST")
    AjaxResult originalHomework(@Validated @RequestBody HomeworkBo homeworkBo);

    @PostMapping("/homework/layeredHomework")
    @ApiOperation(value = "生成分层作业", httpMethod = "POST")
    AjaxResult layeredHomework(@Validated @RequestBody HomeworkBo homeworkBo);

    @PostMapping("/homework/schoolHomework")
    @ApiOperation(value = "生成校本作业", httpMethod = "POST")
    AjaxResult schoolHomework(@Validated @RequestBody HomeworkBo homeworkBo);

}

