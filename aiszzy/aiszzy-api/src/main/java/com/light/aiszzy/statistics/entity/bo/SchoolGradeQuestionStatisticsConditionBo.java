package com.light.aiszzy.statistics.entity.bo;

import com.light.core.entity.PageLimitBo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 作业年级题目正确率
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 10:39:09
 */
@Data
public class SchoolGradeQuestionStatisticsConditionBo extends PageLimitBo{

	/**
	 * 自增主键，唯一标识每一条目录记录
	 */
	@ApiModelProperty("自增主键，唯一标识每一条目录记录")
	private Long id;

	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;

	/**
	 * 关联question题目oid
	 */
	@ApiModelProperty("关联question题目oid")
	private String questionOid;

	/**
	 * 题型id
	 */
	@ApiModelProperty("题型id")
	private String questionTypeId;

	/**
	 * 题型名称
	 */
	@ApiModelProperty("题型名称")
	private String questionTypeName;

	/**
	 * 学科code
	 */
	@ApiModelProperty("学科code")
	private Integer subject;

	/**
	 * 知识点,多个逗号分割
	 */
	@ApiModelProperty("知识点,多个逗号分割")
	private String knowledgePointsId;

	/**
	 * 章节ID
	 */
	@ApiModelProperty("章节ID")
	private String chapterId;

	/**
	 * 节ID
	 */
	@ApiModelProperty("节ID")
	private String sectionId;

	/**
	 * 学校oid
	 */
	@ApiModelProperty("学校oid")
	private String orgCode;

	/**
	 * 年级code
	 */
	@ApiModelProperty("年级code")
	private Integer grade;

	/**
	 * 正确个数
	 */
	@ApiModelProperty("正确个数")
	private Integer rightNum;

	/**
	 * 错误个数
	 */
	@ApiModelProperty("错误个数")
	private Integer wrongNum;

	/**
	 * 提交个数
	 */
	@ApiModelProperty("提交个数")
	private Integer totalNum;

	/**
	 * 班级作业题目正确率小数*100
	 */
	@ApiModelProperty("班级作业题目正确率小数*100")
	private Integer accuracyRate;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;

	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;

	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
