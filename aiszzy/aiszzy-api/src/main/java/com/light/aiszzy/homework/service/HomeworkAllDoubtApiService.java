package com.light.aiszzy.homework.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.homework.api.HomeworkAllDoubtApi;
import com.light.aiszzy.homework.entity.bo.HomeworkAllDoubtBo;
import com.light.aiszzy.homework.entity.bo.HomeworkAllDoubtConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 作业疑问项表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-08-02 15:28:43
 */
@FeignClient(contextId = "homeworkAllDoubtApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = HomeworkAllDoubtApiService.HomeworkAllDoubtApiFallbackFactory.class)
@Component
public interface HomeworkAllDoubtApiService  extends HomeworkAllDoubtApi {

	@Component
	class HomeworkAllDoubtApiFallbackFactory implements FallbackFactory<HomeworkAllDoubtApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(HomeworkAllDoubtApiFallbackFactory.class);
		@Override
		public HomeworkAllDoubtApiService create(Throwable cause) {
			HomeworkAllDoubtApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new HomeworkAllDoubtApiService() {
				public AjaxResult getHomeworkAllDoubtPageListByCondition(HomeworkAllDoubtConditionBo condition){
					return AjaxResult.fail("作业疑问项表查询失败");
				}
				public AjaxResult getHomeworkAllDoubtListByCondition(HomeworkAllDoubtConditionBo condition){
					return AjaxResult.fail("作业疑问项表查询失败");
				}

				public AjaxResult addHomeworkAllDoubt(HomeworkAllDoubtBo homeworkAllDoubtBo){
					return AjaxResult.fail("作业疑问项表新增失败");
				}

				public AjaxResult updateHomeworkAllDoubt(HomeworkAllDoubtBo homeworkAllDoubtBo){
					return AjaxResult.fail("作业疑问项表更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("作业疑问项表获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("作业疑问项表删除失败");
				}
			};
		}
	}
}