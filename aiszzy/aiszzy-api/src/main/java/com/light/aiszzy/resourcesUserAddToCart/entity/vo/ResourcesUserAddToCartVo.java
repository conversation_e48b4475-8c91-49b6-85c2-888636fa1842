package com.light.aiszzy.resourcesUserAddToCart.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 用户加入试题篮信息(试题篮默认每个用户只保存30条试题信息)
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-09 20:20:22
 */
@Data
public class ResourcesUserAddToCartVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * oid
     */
    private String oid;

    /**
     * 用户oid
     */
    private String userOid;

    /**
     * 内部来源oid，question_oid
     */
    private String questionOid;

    /**
     * 学校CODE
     */
    private String orgCode;

    /**
     * 年级code
     */
    private Integer grade;

    /**
     * 学科code
     */
    private Integer subject;

    /**
     * 题目类型id
     */
    private String questionTypeId;

    /**
     * 题目类型名称
     */
    private String questionTypeName;

    /**
     * 大题号
     */
    private String bigNum;

    /**
     * 小题号
     */
    private String smallNum;

    /**
     * 题目url或文字
     */
    private String quesBody;

    /**
     * 题目展示类型  0：图片url  1：html文字 
     */
    private Long quesBodyType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

}
