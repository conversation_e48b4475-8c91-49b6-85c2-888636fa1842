package com.light.aiszzy.homeworkResult.entity.bo;



import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 学生题目答案表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-17 19:40:04
 */
@Data
public class HomeworkResultAnswerBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 学生题目答案表id
	 */
	@ApiModelProperty("学生题目答案表id")
	private Long homeworkResultAnswerId;
	
	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;
	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;
	/**
	 * 作业oid
	 */
	@ApiModelProperty("作业oid")
	private String homeworkOid;
	/**
	 * 学生作业结果oid
	 */
	@ApiModelProperty("学生作业结果oid")
	private String homeworkResultOid;

	/**
	 * 学生姓名
	 */
	@ApiModelProperty("学生姓名")
	private String stuName;
	/**
	 * 学生作业页结果oid
	 */
	@ApiModelProperty("学生作业页结果oid")
	private String homeworkPageResultOid;
	/**
	 * 内容(原，包含学生作答区)
	 */
	@ApiModelProperty("内容(原，包含学生作答区)")
	private String content;
	/**
	 * 内容(不包含学生作答区)
	 */
	@ApiModelProperty("内容(不包含学生作答区)")
	private String smallContent;
	/**
	 * 题目文本内容
	 */
	@ApiModelProperty("题目文本内容")
	private String textContent;
	/**
	 * 位置(原，包含学生作答区),跨页包含多个地址
	 */
	@ApiModelProperty("位置(原，包含学生作答区),跨页包含多个地址")
	private String position;
	/**
	 * 位置(不包含学生作答区)
	 */
	@ApiModelProperty("位置(不包含学生作答区)")
	private String smallPosition;
	/**
	 * 题号
	 */
	@ApiModelProperty("题号")
	private String questionNo;
	/**
	 * 大题号
	 */
	@ApiModelProperty("大题号")
	private String bigNumTitle;
	/**
	 * 小题号
	 */
	@ApiModelProperty("小题号")
	private String smallNumTitle;
	/**
	 * 排序编号
	 */
	@ApiModelProperty("排序编号")
	private String quesOrderNum;
	/**
	 * 题型id
	 */
	@ApiModelProperty("题型id")
	private String questionTypeId;
	/**
	 * 题型名称
	 */
	@ApiModelProperty("题型名称")
	private String questionTypeName;
	/**
	 * 答案
	 */
	@ApiModelProperty("答案")
	private String quesAnswer;
	/**
	 * 章节ID
	 */
	@ApiModelProperty("章节ID")
	private String chapterId;
	/**
	 * 节ID
	 */
	@ApiModelProperty("节ID")
	private String sectionId;
	/**
	 * 知识点，多个
	 */
	@ApiModelProperty("知识点，多个")
	private String knowledgePointsId;
	/**
	 * 学科code
	 */
	@ApiModelProperty("学科code")
	private Integer subject;
	/**
	 * 年级code
	 */
	@ApiModelProperty("年级code")
	private Integer grade;
	/**
	 * 难度
	 */
	@ApiModelProperty("难度")
	private Integer difficultId;
	/**
	 * 启用年份
	 */
	@ApiModelProperty("启用年份")
	private String year;
	/**
	 * 关联题目oid
	 */
	@ApiModelProperty("关联题目oid")
	private String questionOid;
	/**
	 * 学校code
	 */
	@ApiModelProperty("学校code")
	private String orgCode;
	/**
	 * 学生oid
	 */
	@ApiModelProperty("学生oid")
	private String stuOid;
	/**
	 * 页码
	 */
	@ApiModelProperty("页码")
	private Integer stuPageNo;
	/**
	 * 学生班级
	 */
	@ApiModelProperty("学生班级")
	private String stuClassNo;
	/**
	 * 学号
	 */
	@ApiModelProperty("学号")
	private String stuNo;
	/**
	 * 是否正确，1：正确，2：错误，3：未知
	 */
	@ApiModelProperty("是否正确，1：正确，2：错误，3：未知")
	private Integer isCorrect;
	/**
	 * 作答结果
	 */
	@ApiModelProperty("作答结果")
	private String stuAnswer;
	/**
	 * 学生答题路径,跨页包含多个地址
	 */
	@ApiModelProperty("学生答题路径,跨页包含多个地址")
	private String stuAnswerUrls;
	/**
	 * 标记坐标,跨页包含多个地址
	 */
	@ApiModelProperty("标记坐标,跨页包含多个地址")
	private String stuPositions;

	private Integer isDoubt;
	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;
	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;
	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;
	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

	/**
	 * 备注，用来描述题目批改情况，多个用分号分隔
	 */
	@ApiModelProperty("备注，用来描述题目批改情况，多个用分号分隔")
	private String remark;

}
