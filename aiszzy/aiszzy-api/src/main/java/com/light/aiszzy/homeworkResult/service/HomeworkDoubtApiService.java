package com.light.aiszzy.homeworkResult.service;


import com.light.aiszzy.homeworkResult.api.HomeworkDoubtApi;
import com.light.aiszzy.homeworkResult.entity.vo.HomeworkDoubtStatVo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 扫描结构每页处理结果
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 15:52:36
 */
@FeignClient(contextId = "homeworkDoubtApiService", value = "light-aiszzy", configuration = FeignClientInterceptor.class, fallbackFactory = HomeworkDoubtApiService.HomeworkDoubtApiFallbackFactory.class)
@Component
public interface HomeworkDoubtApiService extends HomeworkDoubtApi {

    @Component
    class HomeworkDoubtApiFallbackFactory implements FallbackFactory<HomeworkDoubtApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(HomeworkDoubtApiFallbackFactory.class);

        @Override
        public HomeworkDoubtApiService create(Throwable cause) {
            HomeworkDoubtApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
            return new HomeworkDoubtApiService() {
                @Override
                public AjaxResult<HomeworkDoubtStatVo> stat(String homeworkOid) {
                    return AjaxResult.fail("扫描结构每页处理结果查询失败");
                }
            };
        }
    }
}