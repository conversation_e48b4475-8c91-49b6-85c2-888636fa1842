package com.light.aiszzy.statistics.entity.bo;



import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 题目正确率
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-08-02 18:11:10
 */
@Data
public class QuestionStatisticsBo implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 题目正确率id
	 */
	@ApiModelProperty("题目正确率id")
	private Long questionStatisticsId;
	
	/**
	 * 主键id
	 */
	@ApiModelProperty("主键id")
	private Long id;
	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;
	/**
	 * 关联question题目oid
	 */
	@ApiModelProperty("关联question题目oid")
	private String questionOid;
	/**
	 * 题型id
	 */
	@ApiModelProperty("题型id")
	private String questionTypeId;
	/**
	 * 题型名称
	 */
	@ApiModelProperty("题型名称")
	private String questionTypeName;
	/**
	 * 学科code
	 */
	@ApiModelProperty("学科code")
	private Integer subject;
	/**
	 * 知识点,多个逗号分割
	 */
	@ApiModelProperty("知识点,多个逗号分割")
	private String knowledgePointsId;
	/**
	 * 章节ID
	 */
	@ApiModelProperty("章节ID")
	private String chapterId;
	/**
	 * 节ID
	 */
	@ApiModelProperty("节ID")
	private String sectionId;
	/**
	 * 正确个数
	 */
	@ApiModelProperty("正确个数")
	private Integer rightNum;
	/**
	 * 错误个数
	 */
	@ApiModelProperty("错误个数")
	private Integer wrongNum;
	/**
	 * 提交个数
	 */
	@ApiModelProperty("提交个数")
	private Integer totalNum;
	/**
	 * 题目正确率小数*100
	 */
	@ApiModelProperty("题目正确率小数*100")
	private Integer accuracyRate;
	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;
	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;
	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;
	/**
	 * 是否删除，0：否，1：是
	 */
	@ApiModelProperty("是否删除，0：否，1：是")
	private Integer isDelete;

}
