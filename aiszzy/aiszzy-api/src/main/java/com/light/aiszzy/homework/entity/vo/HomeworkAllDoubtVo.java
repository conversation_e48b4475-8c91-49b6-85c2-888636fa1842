package com.light.aiszzy.homework.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 作业疑问项表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-08-02 15:28:43
 */
@Data
public class HomeworkAllDoubtVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * oid
     */
    private String oid;

    /**
     * 作业oid
     */
    private String homeworkOid;

    /**
     * 作业本oid
     */
    private String homeworkBookOid;

    /**
     * 学校id
     */
    private String orgCode;

    /**
     * 年级code
     */
    private Integer grade;

    /**
     * 学科code
     */
    private Integer subject;

    /**
     * 学期  1:上学期  2：下学期
     */
    private Integer term;

    /**
     * 剩余疑问项数量
     */
    private Integer remainderDoubtCount;

    /**
     * 学年，区间前一个，根据当前时间，升年级时间算出值
     */
    private String year;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

}
