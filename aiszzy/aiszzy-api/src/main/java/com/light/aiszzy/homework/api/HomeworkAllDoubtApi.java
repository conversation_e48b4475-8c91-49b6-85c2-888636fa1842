package com.light.aiszzy.homework.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.homework.entity.vo.HomeworkAllDoubtVo;
import com.light.aiszzy.homework.entity.bo.HomeworkAllDoubtBo;
import com.light.aiszzy.homework.entity.bo.HomeworkAllDoubtConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 作业疑问项表接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-08-02 15:28:43
 */
public interface HomeworkAllDoubtApi  {

	/**
	 * 查询作业疑问项表列表
	 * <AUTHOR>
	 * @date 2025-08-02 15:28:43
	 */
	@PostMapping("/homeworkAllDoubt/pageList")
	@ApiOperation(value = "分页查询作业疑问项表",httpMethod = "POST")
	AjaxResult<PageInfo<HomeworkAllDoubtVo>> getHomeworkAllDoubtPageListByCondition(@RequestBody HomeworkAllDoubtConditionBo condition);

	/**
	 * 查询所有作业疑问项表列表
	 * <AUTHOR>
	 * @date 2025-08-02 15:28:43
	 */
	@PostMapping("/homeworkAllDoubt/list")
	@ApiOperation(value = "查询所有作业疑问项表",httpMethod = "POST")
	AjaxResult<List<HomeworkAllDoubtVo>> getHomeworkAllDoubtListByCondition(@RequestBody HomeworkAllDoubtConditionBo condition);


	/**
	 * 新增作业疑问项表
	 * <AUTHOR>
	 * @date 2025-08-02 15:28:43
	 */
	@PostMapping("/homeworkAllDoubt/add")
	@ApiOperation(value = "新增作业疑问项表",httpMethod = "POST")
	AjaxResult addHomeworkAllDoubt(@Validated @RequestBody HomeworkAllDoubtBo homeworkAllDoubtBo);

	/**
	 * 修改作业疑问项表
	 * @param homeworkAllDoubtBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-08-02 15:28:43
	 */
	@PostMapping("/homeworkAllDoubt/update")
	@ApiOperation(value = "修改作业疑问项表",httpMethod = "POST")
	AjaxResult updateHomeworkAllDoubt(@Validated @RequestBody HomeworkAllDoubtBo homeworkAllDoubtBo);

	/**
	 * 查询作业疑问项表详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-08-02 15:28:43
	 */
	@GetMapping("/homeworkAllDoubt/detail")
	@ApiOperation(value = "查询作业疑问项表详情",httpMethod = "GET")
	AjaxResult<HomeworkAllDoubtVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除作业疑问项表
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-08-02 15:28:43
	 */
	@GetMapping("/homeworkAllDoubt/delete")
	@ApiOperation(value = "删除作业疑问项表",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);



}

