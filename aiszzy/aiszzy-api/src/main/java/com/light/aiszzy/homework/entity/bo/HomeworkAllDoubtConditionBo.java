package com.light.aiszzy.homework.entity.bo;

import com.light.core.entity.PageLimitBo;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

/**
 * 作业疑问项表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-08-02 15:28:43
 */
@Data
public class HomeworkAllDoubtConditionBo extends PageLimitBo{

	/**
	 * 主键
	 */
	@ApiModelProperty("主键")
	private Long id;

	/**
	 * oid
	 */
	@ApiModelProperty("oid")
	private String oid;

	/**
	 * 作业oid
	 */
	@ApiModelProperty("作业oid")
	private String homeworkOid;

	/**
	 * 作业本oid
	 */
	@ApiModelProperty("作业本oid")
	private String homeworkBookOid;

	/**
	 * 学校id
	 */
	@ApiModelProperty("学校id")
	private String orgCode;

	/**
	 * 年级code
	 */
	@ApiModelProperty("年级code")
	private Integer grade;

	/**
	 * 学科code
	 */
	@ApiModelProperty("学科code")
	private Integer subject;

	/**
	 * 学期  1:上学期  2：下学期
	 */
	@ApiModelProperty("学期  1:上学期  2：下学期")
	private Integer term;

	/**
	 * 剩余疑问项数量
	 */
	@ApiModelProperty("剩余疑问项数量")
	private Integer remainderDoubtCount;

	/**
	 * 学年，区间前一个，根据当前时间，升年级时间算出值
	 */
	@ApiModelProperty("学年，区间前一个，根据当前时间，升年级时间算出值")
	private String year;

	/**
	 * 创建时间
	 */
	@ApiModelProperty("创建时间")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@ApiModelProperty("更新时间")
	private Date updateTime;

	/**
	 * 创建者
	 */
	@ApiModelProperty("创建者")
	private String createBy;

	/**
	 * 更新者
	 */
	@ApiModelProperty("更新者")
	private String updateBy;

}
