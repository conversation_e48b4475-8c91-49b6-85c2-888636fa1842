package com.light.aiszzy.homeworkResult.entity.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 学生题目答案表
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-17 19:40:04
 */
@Data
public class HomeworkResultAnswerVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * oid
     */
    private String oid;

    /**
     * 作业oid
     */
    private String homeworkOid;

    private String stuName;

    /**
     * 学生作业结果oid
     */
    private String homeworkResultOid;

    /**
     * 学生作业页结果oid
     */
    private String homeworkPageResultOid;

    /**
     * 内容(原，包含学生作答区)
     */
    private String content;

    /**
     * 内容(不包含学生作答区)
     */
    private String smallContent;

    /**
     * 题目文本内容
     */
    private String textContent;

    /**
     * 位置(原，包含学生作答区),跨页包含多个地址
     */
    private String position;

    /**
     * 位置(不包含学生作答区)
     */
    private String smallPosition;

    /**
     * 题号
     */
    private String questionNo;

    /**
     * 大题号
     */
    private String bigNumTitle;

    /**
     * 小题号
     */
    private String smallNumTitle;

    /**
     * 排序编号
     */
    private String quesOrderNum;

    /**
     * 题型id
     */
    private String questionTypeId;

    /**
     * 题型名称
     */
    private String questionTypeName;

    /**
     * 答案
     */
    private String quesAnswer;

    /**
     * 章节ID
     */
    private String chapterId;

    /**
     * 节ID
     */
    private String sectionId;

    /**
     * 知识点，多个
     */
    private String knowledgePointsId;

    /**
     * 学科code
     */
    private Integer subject;

    /**
     * 年级code
     */
    private Integer grade;

    /**
     * 难度
     */
    private Integer difficultId;

    /**
     * 启用年份
     */
    private String year;

    /**
     * 关联题目oid
     */
    private String questionOid;
    /**
     * 学校code
     */
    private String orgCode;
    /**
     * 学生oid
     */
    private String stuOid;

    /**
     * 页码
     */
    private Integer stuPageNo;

    /**
     * 学生班级
     */
    private Long stuClassId;

    /**
     * 学号
     */
    private String stuNo;

    /**
     * 是否正确，1：正确，2：错误，3：未知
     */
    private Integer isCorrect;

    /**
     * 作答结果
     */
    private String stuAnswer;

    /**
     * 学生答题路径,跨页包含多个地址
     */
    private String stuAnswerUrls;

    /**
     * 标记坐标,跨页包含多个地址
     */
    private String stuPositions;

    private Integer isDoubt;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

    /**
     * 备注，用来描述题目批改情况，多个用分号分隔
     */
    private String remark;

}
