package com.light.aiszzy.homework.service;


import com.light.aiszzy.homework.entity.vo.HomeworkBookVo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.homework.api.HomeworkBookApi;
import com.light.aiszzy.homework.entity.bo.HomeworkBookBo;
import com.light.aiszzy.homework.entity.bo.HomeworkBookConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 作业本接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-07 17:24:33
 */
@FeignClient(contextId = "homeworkBookApiService", value = "light-aiszzy", configuration = FeignClientInterceptor.class, fallbackFactory = HomeworkBookApiService.HomeworkBookApiFallbackFactory.class)
@Component
public interface HomeworkBookApiService extends HomeworkBookApi {

    @Component
    class HomeworkBookApiFallbackFactory implements FallbackFactory<HomeworkBookApiService> {
        private static final Logger LOGGER = LoggerFactory.getLogger(HomeworkBookApiService.HomeworkBookApiFallbackFactory.class);

        @Override
        public HomeworkBookApiService create(Throwable cause) {
            HomeworkBookApiService.HomeworkBookApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
            return new HomeworkBookApiService() {
                public AjaxResult getHomeworkBookPageListByCondition(HomeworkBookConditionBo condition) {
                    return AjaxResult.fail("作业本查询失败");
                }

                public AjaxResult getHomeworkBookListByCondition(HomeworkBookConditionBo condition) {
                    return AjaxResult.fail("作业本查询失败");
                }

                @Override
                public AjaxResult getHomeworkBookGroupListByCondition(HomeworkBookConditionBo condition) {
                    return AjaxResult.fail("作业本查询失败");
                }

                @Override
                public AjaxResult currentBook(HomeworkBookConditionBo condition) {
                    return AjaxResult.fail("作业本查询失败");
                }

                @Override
                public AjaxResult<HomeworkBookVo> updateCurrentBook(HomeworkBookBo condition) {
                    return AjaxResult.fail("更新当前作业本失败");
                }

                @Override
                public AjaxResult getSchoolNotAddHomeworkBookListByCondition(HomeworkBookConditionBo condition) {
                    return AjaxResult.fail("分页查询未添加学校作业本失败");
                }

                public AjaxResult addHomeworkBook(HomeworkBookBo homeworkBookBo) {
                    return AjaxResult.fail("作业本新增失败");
                }

                public AjaxResult updateHomeworkBook(HomeworkBookBo homeworkBookBo) {
                    return AjaxResult.fail("作业本更新失败");
                }

                public AjaxResult getDetail(String oid) {
                    return AjaxResult.fail("作业本获取详情失败");
                }

                public AjaxResult delete(String oid) {
                    return AjaxResult.fail("作业本删除失败");
                }

                @Override
                public AjaxResult textbookVersions(String grade, String subject) {
                    return AjaxResult.fail("获取教材版本失败");
                }

                @Override
                public AjaxResult textbook(String versionId, String gradeId) {
                    return AjaxResult.fail("获取教材失败");
                }

                @Override
                public AjaxResult textbookCatalog(String versionId) {
                    return AjaxResult.fail("获取目录失败");
                }

                @Override
                public AjaxResult saveTextbookCatalog(String oid, String textBookId) {
                    return AjaxResult.fail("获取目录失败");
                }

                @Override
                public AjaxResult checkTextbookCatalog(String oid) {
                    return AjaxResult.fail("获取是否存在失败");
                }

                @Override
                public AjaxResult<HomeworkBookVo> practiceBookToHomeworkBook(HomeworkBookBo homeworkBookBo) {
                    return AjaxResult.fail("同步教辅到作业本失败");
                }

                @Override
                public AjaxResult<HomeworkBookVo> copyHomeworkBook(HomeworkBookBo homeworkBookBo) {
                    return AjaxResult.fail("复制作业本失败");
                }

                @Override
                public AjaxResult downloadBookZip(String oid) {
                    return AjaxResult.fail("作业本下载失败");
                }

                @Override
                public AjaxResult downloadBookPdf(String oid) {
                    return AjaxResult.fail("下载作业本答案合并一个pdf失败");
                }

            };
        }
    }
}