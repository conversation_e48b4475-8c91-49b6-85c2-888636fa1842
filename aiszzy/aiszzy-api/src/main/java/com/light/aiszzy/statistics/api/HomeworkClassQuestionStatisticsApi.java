package com.light.aiszzy.statistics.api;

import com.github.pagehelper.PageInfo;
import com.light.aiszzy.statistics.entity.bo.HomeworkClassQuestionStatisticsBo;
import com.light.aiszzy.statistics.entity.bo.HomeworkClassQuestionStatisticsConditionBo;
import com.light.aiszzy.statistics.entity.vo.HomeworkClassQuestionStatisticsVo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 作业班级题目正确率接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 10:39:09
 */
public interface HomeworkClassQuestionStatisticsApi  {

	/**
	 * 查询作业班级题目正确率列表
	 * <AUTHOR>
	 * @date 2025-07-30 10:39:09
	 */
	@PostMapping("/homeworkClassQuestionStatistics/pageList")
	@ApiOperation(value = "分页查询作业班级题目正确率",httpMethod = "POST")
	AjaxResult<PageInfo<HomeworkClassQuestionStatisticsVo>> getHomeworkClassQuestionStatisticsPageListByCondition(@RequestBody HomeworkClassQuestionStatisticsConditionBo condition);

	/**
	 * 查询所有作业班级题目正确率列表
	 * <AUTHOR>
	 * @date 2025-07-30 10:39:09
	 */
	@PostMapping("/homeworkClassQuestionStatistics/list")
	@ApiOperation(value = "查询所有作业班级题目正确率",httpMethod = "POST")
	AjaxResult<List<HomeworkClassQuestionStatisticsVo>> getHomeworkClassQuestionStatisticsListByCondition(@RequestBody HomeworkClassQuestionStatisticsConditionBo condition);


	/**
	 * 新增作业班级题目正确率
	 * <AUTHOR>
	 * @date 2025-07-30 10:39:09
	 */
	@PostMapping("/homeworkClassQuestionStatistics/add")
	@ApiOperation(value = "新增作业班级题目正确率",httpMethod = "POST")
	AjaxResult addHomeworkClassQuestionStatistics(@Validated @RequestBody HomeworkClassQuestionStatisticsBo homeworkClassQuestionStatisticsBo);

	/**
	 * 修改作业班级题目正确率
	 * @param homeworkClassQuestionStatisticsBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-30 10:39:09
	 */
	@PostMapping("/homeworkClassQuestionStatistics/update")
	@ApiOperation(value = "修改作业班级题目正确率",httpMethod = "POST")
	AjaxResult updateHomeworkClassQuestionStatistics(@Validated @RequestBody HomeworkClassQuestionStatisticsBo homeworkClassQuestionStatisticsBo);

	/**
	 * 查询作业班级题目正确率详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-30 10:39:09
	 */
	@GetMapping("/homeworkClassQuestionStatistics/detail")
	@ApiOperation(value = "查询作业班级题目正确率详情",httpMethod = "GET")
	AjaxResult<HomeworkClassQuestionStatisticsVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除作业班级题目正确率
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-30 10:39:09
	 */
	@GetMapping("/homeworkClassQuestionStatistics/delete")
	@ApiOperation(value = "删除作业班级题目正确率",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);

	/**
	 * 查询班级题目正确率
	 * @param homeworkClassQuestionStatisticsBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-07-30 10:39:09
	 */
	@PostMapping("/homeworkClassQuestionStatistics/queryByHomeworkClass")
	@ApiOperation(value = "查询班级题目正确率",httpMethod = "POST")
	AjaxResult<List<HomeworkClassQuestionStatisticsVo>> queryByHomeworkClass(@Validated @RequestBody HomeworkClassQuestionStatisticsBo homeworkClassQuestionStatisticsBo);


}

