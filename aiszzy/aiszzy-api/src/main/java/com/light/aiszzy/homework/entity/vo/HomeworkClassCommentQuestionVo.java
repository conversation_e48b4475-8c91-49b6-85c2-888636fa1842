package com.light.aiszzy.homework.entity.vo;

import com.light.aiszzy.question.entity.vo.QuestionVo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
/**
 * 班级作业讲评题目
 * 
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 10:39:09
 */
@Data
public class HomeworkClassCommentQuestionVo implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键，唯一标识每一条目录记录
     */
    private Long id;

    /**
     * oid
     */
    private String oid;

    /**
     * 学校oid
     */
    private String orgCode;

    /**
     * 学科code
     */
    private Integer subject;

    /**
     * 年级code
     */
    private Integer grade;

    /**
     * 班级id
     */
    private Long classId;

    /**
     * 作业oid
     */
    private String homeworkOid;

    /**
     * 关联question题目oid
     */
    private String questionOid;

    /**
     * 讲评原题question题目oid
     */
    private String commentQuestionOid;

    /**
     * 题目排序
     */
    private Long quesOrderNum;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDelete;

    private QuestionVo questionVo;

}
