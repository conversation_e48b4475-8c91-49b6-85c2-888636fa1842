package com.light.aiszzy.statistics.service;


import com.light.aiszzy.statistics.api.HomeworkClassQuestionStatisticsApi;
import com.light.aiszzy.statistics.entity.bo.HomeworkClassQuestionStatisticsBo;
import com.light.aiszzy.statistics.entity.bo.HomeworkClassQuestionStatisticsConditionBo;
import com.light.aiszzy.statistics.entity.vo.HomeworkClassQuestionStatisticsVo;
import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 作业班级题目正确率接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-07-30 10:39:09
 */
@FeignClient(contextId = "homeworkClassQuestionStatisticsApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = HomeworkClassQuestionStatisticsApiService.HomeworkClassQuestionStatisticsApiFallbackFactory.class)
@Component
public interface HomeworkClassQuestionStatisticsApiService  extends HomeworkClassQuestionStatisticsApi {

	@Component
	class HomeworkClassQuestionStatisticsApiFallbackFactory implements FallbackFactory<HomeworkClassQuestionStatisticsApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(HomeworkClassQuestionStatisticsApiFallbackFactory.class);
		@Override
		public HomeworkClassQuestionStatisticsApiService create(Throwable cause) {
			HomeworkClassQuestionStatisticsApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new HomeworkClassQuestionStatisticsApiService() {
				public AjaxResult getHomeworkClassQuestionStatisticsPageListByCondition(HomeworkClassQuestionStatisticsConditionBo condition){
					return AjaxResult.fail("作业班级题目正确率查询失败");
				}
				public AjaxResult getHomeworkClassQuestionStatisticsListByCondition(HomeworkClassQuestionStatisticsConditionBo condition){
					return AjaxResult.fail("作业班级题目正确率查询失败");
				}

				public AjaxResult addHomeworkClassQuestionStatistics(HomeworkClassQuestionStatisticsBo homeworkClassQuestionStatisticsBo){
					return AjaxResult.fail("作业班级题目正确率新增失败");
				}

				public AjaxResult updateHomeworkClassQuestionStatistics(HomeworkClassQuestionStatisticsBo homeworkClassQuestionStatisticsBo){
					return AjaxResult.fail("作业班级题目正确率更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("作业班级题目正确率获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("作业班级题目正确率删除失败");
				}

				@Override
				public AjaxResult<List<HomeworkClassQuestionStatisticsVo>> queryByHomeworkClass(HomeworkClassQuestionStatisticsBo homeworkClassQuestionStatisticsBo) {
					return AjaxResult.fail("查询班级题目正确率失败");
				}
			};
		}
	}
}