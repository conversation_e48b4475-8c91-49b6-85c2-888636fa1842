package com.light.aiszzy.statistics.api;

import com.github.pagehelper.PageInfo;
import com.light.core.entity.AjaxResult;
import com.light.aiszzy.statistics.entity.vo.QuestionStatisticsVo;
import com.light.aiszzy.statistics.entity.bo.QuestionStatisticsBo;
import com.light.aiszzy.statistics.entity.bo.QuestionStatisticsConditionBo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 题目正确率接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-08-02 18:11:10
 */
public interface QuestionStatisticsApi  {

	/**
	 * 查询题目正确率列表
	 * <AUTHOR>
	 * @date 2025-08-02 18:11:10
	 */
	@PostMapping("/questionStatistics/pageList")
	@ApiOperation(value = "分页查询题目正确率",httpMethod = "POST")
	AjaxResult<PageInfo<QuestionStatisticsVo>> getQuestionStatisticsPageListByCondition(@RequestBody QuestionStatisticsConditionBo condition);

	/**
	 * 查询所有题目正确率列表
	 * <AUTHOR>
	 * @date 2025-08-02 18:11:10
	 */
	@PostMapping("/questionStatistics/list")
	@ApiOperation(value = "查询所有题目正确率",httpMethod = "POST")
	AjaxResult<List<QuestionStatisticsVo>> getQuestionStatisticsListByCondition(@RequestBody QuestionStatisticsConditionBo condition);


	/**
	 * 新增题目正确率
	 * <AUTHOR>
	 * @date 2025-08-02 18:11:10
	 */
	@PostMapping("/questionStatistics/add")
	@ApiOperation(value = "新增题目正确率",httpMethod = "POST")
	AjaxResult addQuestionStatistics(@Validated @RequestBody QuestionStatisticsBo questionStatisticsBo);

	/**
	 * 修改题目正确率
	 * @param questionStatisticsBo
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-08-02 18:11:10
	 */
	@PostMapping("/questionStatistics/update")
	@ApiOperation(value = "修改题目正确率",httpMethod = "POST")
	AjaxResult updateQuestionStatistics(@Validated @RequestBody QuestionStatisticsBo questionStatisticsBo);

	/**
	 * 查询题目正确率详情
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-08-02 18:11:10
	 */
	@GetMapping("/questionStatistics/detail")
	@ApiOperation(value = "查询题目正确率详情",httpMethod = "GET")
	AjaxResult<QuestionStatisticsVo> getDetail(@NotNull(message = "请选择数据") @RequestParam("oid") String oid);

	/**
	 * 删除题目正确率
	 * @param oid
	 * @return
	 * @returnType AjaxResult
	 * <AUTHOR>
	 * @date 2025-08-02 18:11:10
	 */
	@GetMapping("/questionStatistics/delete")
	@ApiOperation(value = "删除题目正确率",httpMethod = "GET")
	@ApiImplicitParam(name = "id", value = "oid", required = true, dataType = "String", paramType = "delete")
	AjaxResult delete(@NotNull(message = "请选择需要删除的数据") @RequestParam("oid") String oid);



}

