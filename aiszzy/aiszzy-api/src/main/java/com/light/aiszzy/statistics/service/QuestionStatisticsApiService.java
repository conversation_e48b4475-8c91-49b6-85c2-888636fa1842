package com.light.aiszzy.statistics.service;


import com.light.core.entity.AjaxResult;
import com.light.feign.interceptors.FeignClientInterceptor;
import com.light.aiszzy.statistics.api.QuestionStatisticsApi;
import com.light.aiszzy.statistics.entity.bo.QuestionStatisticsBo;
import com.light.aiszzy.statistics.entity.bo.QuestionStatisticsConditionBo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


/**
 * 题目正确率接口
 *
 * <AUTHOR>
 * @email ${email}
 * @date 2025-08-02 18:11:10
 */
@FeignClient(contextId = "questionStatisticsApiService", value="light-aiszzy" ,configuration = FeignClientInterceptor.class, fallbackFactory = QuestionStatisticsApiService.QuestionStatisticsApiFallbackFactory.class)
@Component
public interface QuestionStatisticsApiService  extends QuestionStatisticsApi {

	@Component
	class QuestionStatisticsApiFallbackFactory implements FallbackFactory<QuestionStatisticsApiService> {
		private static final Logger LOGGER = LoggerFactory.getLogger(QuestionStatisticsApiFallbackFactory.class);
		@Override
		public QuestionStatisticsApiService create(Throwable cause) {
			QuestionStatisticsApiFallbackFactory.LOGGER.error("用户服务调用失败:{}", cause.getMessage());
			return new QuestionStatisticsApiService() {
				public AjaxResult getQuestionStatisticsPageListByCondition(QuestionStatisticsConditionBo condition){
					return AjaxResult.fail("题目正确率查询失败");
				}
				public AjaxResult getQuestionStatisticsListByCondition(QuestionStatisticsConditionBo condition){
					return AjaxResult.fail("题目正确率查询失败");
				}

				public AjaxResult addQuestionStatistics(QuestionStatisticsBo questionStatisticsBo){
					return AjaxResult.fail("题目正确率新增失败");
				}

				public AjaxResult updateQuestionStatistics(QuestionStatisticsBo questionStatisticsBo){
					return AjaxResult.fail("题目正确率更新失败");
				}

				public AjaxResult getDetail(String oid){
					return AjaxResult.fail("题目正确率获取详情失败");
				}

				public AjaxResult delete(String oid){
					return AjaxResult.fail("题目正确率删除失败");
				}
			};
		}
	}
}