package com.light.aiszzy.homeworkResult.api;

import com.light.aiszzy.homeworkResult.entity.vo.HomeworkDoubtStatVo;
import com.light.core.entity.AjaxResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotNull;

public interface HomeworkDoubtApi {

    /**
     * 统计当前作业疑问项
     *
     * @param homeworkOid 作业oid
     * @return AjaxResult
     */
    @GetMapping("/homeworkDoubt/stat")
    @ApiOperation(value = "统计当前作业疑问项", httpMethod = "GET")
    AjaxResult<HomeworkDoubtStatVo> stat(@NotNull(message = "请选择作业") @RequestParam("homeworkOid") String homeworkOid);

}
